import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/rn_components/rn_search_bar.dart';
import 'package:culture_connect/widgets/rn_components/rn_category_pill.dart';
import 'package:culture_connect/widgets/rn_components/rn_quick_action_button.dart';
import 'package:culture_connect/widgets/rn_components/rn_promotional_banner.dart';
import 'package:culture_connect/widgets/rn_components/rn_ai_recommendation_card.dart';
import 'package:culture_connect/widgets/rn_components/rn_destination_card.dart';
import 'package:culture_connect/widgets/rn_components/rn_top_place_card.dart';
import 'package:culture_connect/widgets/rn_components/rn_top_guide_card.dart';

import 'package:culture_connect/screens/luxury_services_screen.dart';

/// Pixel-perfect React Native Home Screen replica
/// Matches the exact design from React Native reference images
class RNHomeScreen extends ConsumerStatefulWidget {
  const RNHomeScreen({super.key});

  @override
  ConsumerState<RNHomeScreen> createState() => _RNHomeScreenState();
}

class _RNHomeScreenState extends ConsumerState<RNHomeScreen> {
  final ScrollController _scrollController = ScrollController();

  // React Native Categories - exact match
  final List<String> _categories = [
    'All',
    'Popular',
    'Cultural',
    'Adventure',
    'Beach',
    'Mountain',
    'City',
    'Food',
  ];

  String _selectedCategory = 'All';
  String _searchQuery = '';
  final int _unreadNotifications = 3; // Mock notification count

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Mock data matching React Native reference
  final List<Map<String, dynamic>> _mockDestinations = [
    {
      'id': '1',
      'title': 'Traditional Tea Ceremony Experience',
      'location': 'Kyoto, Japan',
      'imageUrl':
          'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.8,
      'reviewCount': 124,
      'price': 'From \$45',
      'duration': '2 hours',
      'tags': ['Cultural', 'Traditional', 'Tea'],
    },
    {
      'id': '2',
      'title': 'Street Food Walking Tour',
      'location': 'Bangkok, Thailand',
      'imageUrl':
          'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.9,
      'reviewCount': 89,
      'price': 'From \$35',
      'duration': '3 hours',
      'tags': ['Food', 'Street Food', 'Walking'],
    },
    {
      'id': '3',
      'title': 'Flamenco Dance Workshop',
      'location': 'Seville, Spain',
      'imageUrl':
          'https://images.unsplash.com/photo-1504609813442-a8924e83f76e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.7,
      'reviewCount': 156,
      'price': 'From \$60',
      'duration': '1.5 hours',
      'tags': ['Dance', 'Cultural', 'Workshop'],
    },
  ];

  // Mock data for Top Places section
  final List<Map<String, dynamic>> _mockTopPlaces = [
    {
      'id': '1',
      'title': 'Santorini Sunset Views',
      'location': 'Santorini, Greece',
      'imageUrl':
          'https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.9,
      'reviewCount': 234,
    },
    {
      'id': '2',
      'title': 'Machu Picchu Ancient Ruins',
      'location': 'Cusco, Peru',
      'imageUrl':
          'https://images.unsplash.com/photo-1587595431973-160d0d94add1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.8,
      'reviewCount': 189,
    },
    {
      'id': '3',
      'title': 'Bali Rice Terraces',
      'location': 'Ubud, Indonesia',
      'imageUrl':
          'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.7,
      'reviewCount': 156,
    },
    {
      'id': '4',
      'title': 'Northern Lights',
      'location': 'Reykjavik, Iceland',
      'imageUrl':
          'https://images.unsplash.com/photo-1531366936337-7c912a4589a7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.9,
      'reviewCount': 298,
    },
  ];

  // Mock data for Top Guides section - Updated to match reference design
  final List<Map<String, dynamic>> _mockTopGuides = [
    {
      'id': '1',
      'name': 'Nolan W.',
      'specialty': 'Adventure Tours',
      'location': 'Tokyo, Japan',
      'imageUrl':
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
      'rating': 4.8,
      'reviewCount': 93,
      'experience': '12+ years',
      'isCertified': true,
    },
    {
      'id': '2',
      'name': 'Maria Santos',
      'specialty': 'Cultural Heritage',
      'location': 'Barcelona, Spain',
      'imageUrl':
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
      'rating': 4.9,
      'reviewCount': 127,
      'experience': '8+ years',
      'isCertified': true,
    },
    {
      'id': '3',
      'name': 'Elena Rodriguez',
      'specialty': 'Food & Wine',
      'location': 'Valencia, Spain', // Added missing location field
      'imageUrl':
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
      'rating': 4.9,
      'reviewCount': 156,
      'experience': '10+ years',
      'isCertified': true, // Added missing certification field
    },
    {
      'id': '4',
      'name': 'Ahmed Hassan',
      'specialty': 'Historical Sites',
      'location': 'Cairo, Egypt', // Added missing location field
      'imageUrl':
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
      'rating': 4.7,
      'reviewCount': 89,
      'experience': '6+ years',
      'isCertified': true, // Added missing certification field
    },
  ];

  // Mock data for Upcoming Events section
  final List<Map<String, dynamic>> _mockUpcomingEvents = [
    {
      'id': '1',
      'title': 'Tokyo Cherry Blossom Festival',
      'date': 'March 25, 2025',
      'time': '10:00 AM',
      'location': 'Ueno Park, Tokyo',
      'imageUrl':
          'https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Festival',
      'price': 'Free',
      'attendees': 1250,
      'isBookmarked': false,
    },
    {
      'id': '2',
      'title': 'Barcelona Food & Wine Tour',
      'date': 'April 12, 2025',
      'time': '6:00 PM',
      'location': 'Gothic Quarter, Barcelona',
      'imageUrl':
          'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'Food Tour',
      'price': '€45',
      'attendees': 85,
      'isBookmarked': true,
    },
    {
      'id': '3',
      'title': 'Cairo Historical Walking Tour',
      'date': 'April 20, 2025',
      'time': '9:00 AM',
      'location': 'Islamic Cairo, Egypt',
      'imageUrl':
          'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'category': 'History',
      'price': r'$25',
      'attendees': 156,
      'isBookmarked': false,
    },
  ];

  // Mock data for AI Powered Suggestions section
  final List<Map<String, dynamic>> _mockAISuggestions = [
    {
      'id': '1',
      'title': 'Perfect Weekend in Kyoto',
      'description': 'Based on your love for cultural sites and temples',
      'imageUrl':
          'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'aiConfidence': 95,
      'duration': '2 days',
      'price': '¥12,500',
      'tags': ['Cultural', 'Temples', 'Traditional'],
      'matchReason': 'Matches your interest in Japanese culture',
    },
    {
      'id': '2',
      'title': 'Mediterranean Coastal Adventure',
      'description': 'Recommended for adventure seekers like you',
      'imageUrl':
          'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'aiConfidence': 88,
      'duration': '5 days',
      'price': '€850',
      'tags': ['Adventure', 'Coastal', 'Hiking'],
      'matchReason': 'Based on your adventure tour preferences',
    },
    {
      'id': '3',
      'title': 'Authentic Street Food Journey',
      'description': 'Curated for food enthusiasts',
      'imageUrl':
          'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      'aiConfidence': 92,
      'duration': '1 day',
      'price': r'$65',
      'tags': ['Food', 'Local', 'Authentic'],
      'matchReason': 'Perfect match for your culinary interests',
    },
  ];

  void _handleQuickAction(String action) {
    switch (action) {
      case 'hotel':
        // Navigate to hotel booking
        break;
      case 'tours':
        // Navigate to tour packages
        break;
      case 'flights':
        // Navigate to flight booking
        break;
      case 'cars':
        // Navigate to car rental
        break;
      case 'luxury':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const LuxuryServicesScreen()),
        );
        break;
      case 'food':
        // Navigate to food experiences
        break;
      case 'visa':
        // Navigate to visa services
        break;
      case 'medical':
        // Navigate to medical services
        break;
    }
  }

  Future<void> _handleRefresh() async {
    // Simulate refresh
    await Future.delayed(const Duration(seconds: 1));
  }

  void _handleNotificationPress() {
    // Handle notification press - show overlay or navigate
    // TODO: Implement notification overlay
  }

  void _handleDestinationPress(String destinationId) {
    // Navigate to destination details
    // TODO: Navigate to proper destination details screen
    // For now, just show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening destination: $destinationId')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _handleRefresh,
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section - Exact React Native match
                _buildHeader(),

                // Search Bar Section
                _buildSearchSection(),

                // Quick Services Section
                _buildQuickServicesSection(),

                // Promotional Banner Section
                _buildPromotionalBannerSection(),

                // Categories Section
                _buildCategoriesSection(),

                // AI Recommendation Section
                _buildAIRecommendationSection(),

                // Trending Experiences Section
                _buildTrendingExperiencesSection(),

                // Top Places Section
                _buildTopPlacesSection(),

                // Top Guides Section
                _buildTopGuidesSection(),

                // Upcoming Events Section
                _buildUpcomingEventsSection(),

                // AI Powered Suggestions Section
                _buildAIPoweredSuggestionsSection(),

                // Bottom padding
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top row with greeting and notification
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Hello, Guest',
                style: TextStyle(
                  fontSize: AppTheme.fontSizeLg, // 18px
                  fontWeight: AppTheme.fontWeightMedium,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              // Notification Button
              GestureDetector(
                onTap: _handleNotificationPress,
                child: Stack(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppTheme.backgroundSecondary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.notifications_outlined,
                        size: 24,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),

                    // Notification Badge
                    if (_unreadNotifications > 0)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: 18,
                          height: 18,
                          decoration: const BoxDecoration(
                            color: AppTheme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              _unreadNotifications > 9
                                  ? '9+'
                                  : _unreadNotifications.toString(),
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Main Title
          const Text(
            'Let\'s Explore the World together',
            style: TextStyle(
              fontSize: AppTheme.fontSizeXxl, // 28px
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: RNSearchBar(
        value: _searchQuery,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        placeholder: 'Search destination and explore',
        onFilterPressed: () {
          // Handle filter press
          print('Filter pressed');
        },
        onClearPressed: () {
          setState(() {
            _searchQuery = '';
          });
        },
      ),
    );
  }

  Widget _buildQuickServicesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Quick Services',
              style: TextStyle(
                fontSize: AppTheme.fontSizeLg, // 18px
                fontWeight: AppTheme.fontWeightBold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Horizontal scrolling quick actions with defensive constraints
          Container(
            height:
                120, // Increased from 110 to provide extra buffer for all scenarios
            constraints: const BoxConstraints(
              minHeight: 120,
              maxHeight: 120,
            ),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: RNQuickActions.actions.length,
              // Prevent overflow during IndexedStack offstage rendering
              cacheExtent: 0,
              addAutomaticKeepAlives: false,
              addRepaintBoundaries: false,
              itemBuilder: (context, index) {
                final action = RNQuickActions.actions[index];
                return RNQuickActionButton(
                  title: action.title,
                  icon: action.icon,
                  backgroundColor: action.backgroundColor,
                  iconColor: action.iconColor,
                  onPressed: () => _handleQuickAction(action.action),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromotionalBannerSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
      child: RNPromotionalBanner(
        title: 'SAVE UP TO',
        subtitle: '20% WITH Explorer+',
        imageUrl:
            'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
        onPressed: () {
          // Handle promotional banner press
          print('Promotional banner pressed');
        },
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Explore by category',
              style: TextStyle(
                fontSize: AppTheme.fontSizeLg, // 18px
                fontWeight: AppTheme.fontWeightBold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Horizontal scrolling category pills
          SizedBox(
            height: 56,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return RNCategoryPill(
                  label: category,
                  isSelected: _selectedCategory == category,
                  onPressed: () {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIRecommendationSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
      child: RNAIRecommendationCard(
        title: 'Your Perfect Weekend Getaway',
        description:
            'Based on your preferences, we think you will love this cultural experience in Kyoto.',
        imageUrl:
            'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
        onPressed: () {
          // Handle AI recommendation press
          _handleDestinationPress('1');
        },
      ),
    );
  }

  Widget _buildTrendingExperiencesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Trending Experiences',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLg, // 18px
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // Handle see all press
                    print('See all pressed');
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      fontWeight: AppTheme.fontWeightMedium,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Destination Cards
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: _mockDestinations.map((destination) {
                return RNDestinationCard(
                  title: destination['title'],
                  location: destination['location'],
                  imageUrl: destination['imageUrl'],
                  rating: destination['rating'],
                  reviewCount: destination['reviewCount'],
                  price: destination['price'],
                  duration: destination['duration'],
                  tags: List<String>.from(destination['tags']),
                  onPressed: () => _handleDestinationPress(destination['id']),
                  onFavoritePressed: () {
                    // Handle favorite press
                    print('Favorite pressed for ${destination['id']}');
                  },
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopPlacesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Top Places',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLg, // 18px
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // Handle see all press
                    print('Top Places - See all pressed');
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      fontWeight: AppTheme.fontWeightMedium,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Horizontal Scrolling Places Cards
          SizedBox(
            height: 180, // Fixed height for horizontal scrolling
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _mockTopPlaces.length,
              itemBuilder: (context, index) {
                final place = _mockTopPlaces[index];
                return RNTopPlaceCard(
                  title: place['title'],
                  location: place['location'],
                  imageUrl: place['imageUrl'],
                  rating: place['rating'],
                  reviewCount: place['reviewCount'],
                  onPressed: () => _handleDestinationPress(place['id']),
                  onFavoritePressed: () {
                    // Handle favorite press
                    print('Favorite pressed for place ${place['id']}');
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopGuidesSection() {
    return Padding(
      padding: const EdgeInsets.only(
          top: 32), // Increased spacing for better visual separation (was 24px)
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Top Guides',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLg, // 18px
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // Handle see all press
                    print('Top Guides - See all pressed');
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      fontWeight: AppTheme.fontWeightMedium,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Horizontal Scrolling Guide Cards
          SizedBox(
            height:
                280, // Increased height to prevent bottom clipping and match reference design (was 252px)
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _mockTopGuides.length,
              itemBuilder: (context, index) {
                final guide = _mockTopGuides[index];
                return RNTopGuideCard(
                  name: guide['name'] ??
                      'Unknown Guide', // Defensive null handling
                  specialty: guide['specialty'] ??
                      'General Tours', // Defensive null handling
                  location: guide['location'] ??
                      'Location TBD', // Defensive null handling for required field
                  imageUrl: guide['imageUrl'] ?? '', // Defensive null handling
                  rating: (guide['rating'] as num?)?.toDouble() ??
                      4.0, // Defensive null handling with type safety
                  reviewCount:
                      guide['reviewCount'] ?? 0, // Defensive null handling
                  experience: guide['experience'] ??
                      '1+ years', // Defensive null handling
                  isCertified:
                      guide['isCertified'] ?? true, // Defensive null handling
                  onPressed: () => _handleGuidePress(guide['id'] ?? ''),
                  onFavoritePressed: () {
                    // Handle favorite press
                    print('Favorite pressed for guide ${guide['id']}');
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _handleGuidePress(String guideId) {
    // Navigate to guide details
    // TODO: Navigate to proper guide details screen
    // For now, just show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening guide: $guideId')),
    );
  }

  // Upcoming Events Section
  Widget _buildUpcomingEventsSection() {
    return Padding(
      padding: const EdgeInsets.only(
          top: 32), // Consistent spacing with other sections
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Upcoming Events',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLg, // 20px
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                TextButton(
                  onPressed: () => _handleSeeAllPress('events'),
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Horizontal Scrolling Event Cards
          SizedBox(
            height: 280, // Adequate height for event cards
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              itemCount: _mockUpcomingEvents.length,
              itemBuilder: (context, index) {
                final event = _mockUpcomingEvents[index];
                return Container(
                  width: 280, // Card width
                  margin: EdgeInsets.only(
                      right: index < _mockUpcomingEvents.length - 1 ? 16 : 0),
                  child: _buildEventCard(event),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // AI Powered Suggestions Section
  Widget _buildAIPoweredSuggestionsSection() {
    return Padding(
      padding: const EdgeInsets.only(
          top: 32), // Consistent spacing with other sections
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header with AI Badge
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Text(
                      'AI Powered Suggestions',
                      style: TextStyle(
                        fontSize: AppTheme.fontSizeLg, // 20px
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.auto_awesome,
                            size: 12,
                            color: AppTheme.primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'AI',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () => _handleSeeAllPress('ai-suggestions'),
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Horizontal Scrolling AI Suggestion Cards
          SizedBox(
            height: 320, // Adequate height for AI suggestion cards
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              itemCount: _mockAISuggestions.length,
              itemBuilder: (context, index) {
                final suggestion = _mockAISuggestions[index];
                return Container(
                  width: 300, // Card width
                  margin: EdgeInsets.only(
                      right: index < _mockAISuggestions.length - 1 ? 16 : 0),
                  child: _buildAISuggestionCard(suggestion),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Event Card Widget
  Widget _buildEventCard(Map<String, dynamic> event) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _handleEventPress(event['id']),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event Image with Bookmark
            Stack(
              children: [
                ClipRRect(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                  child: Image.network(
                    event['imageUrl'] ?? '',
                    height: 140,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 140,
                        color: Colors.grey[300],
                        child: const Icon(Icons.image_not_supported),
                      );
                    },
                  ),
                ),
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      event['isBookmarked'] == true
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                      size: 20,
                      color: event['isBookmarked'] == true
                          ? AppTheme.primaryColor
                          : AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
                Positioned(
                  top: 12,
                  left: 12,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      event['category'] ?? '',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Event Details - Wrapped in Expanded to prevent overflow
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      event['title'] ?? '',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                        const SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            event['date'] ?? '',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Icon(
                          Icons.access_time,
                          size: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                        const SizedBox(width: 4),
                        Flexible(
                          child: Text(
                            event['time'] ?? '',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        const Icon(
                          Icons.location_on,
                          size: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            event['location'] ?? '',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(
                            event['price'] ?? '',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        Flexible(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.people,
                                size: 14,
                                color: AppTheme.textSecondaryColor,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${event['attendees'] ?? 0}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.textSecondaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // AI Suggestion Card Widget
  Widget _buildAISuggestionCard(Map<String, dynamic> suggestion) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _handleAISuggestionPress(suggestion['id']),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Suggestion Image with AI Badge
            Stack(
              children: [
                ClipRRect(
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                  child: Image.network(
                    suggestion['imageUrl'] ?? '',
                    height: 160,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 160,
                        color: Colors.grey[300],
                        child: const Icon(Icons.image_not_supported),
                      );
                    },
                  ),
                ),
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.auto_awesome,
                          size: 12,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${suggestion['aiConfidence'] ?? 0}%',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Suggestion Details - Wrapped in Expanded to prevent overflow
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      suggestion['title'] ?? '',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),
                    Text(
                      suggestion['description'] ?? '',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      suggestion['matchReason'] ?? '',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppTheme.primaryColor,
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(
                            suggestion['price'] ?? '',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        Flexible(
                          child: Text(
                            suggestion['duration'] ?? '',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 6,
                      children: (suggestion['tags'] as List<String>? ?? [])
                          .take(3)
                          .map((tag) => Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  tag,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: AppTheme.primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ))
                          .toList(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Event handlers
  void _handleEventPress(String eventId) {
    // TODO: Navigate to event details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening event: $eventId')),
    );
  }

  void _handleAISuggestionPress(String suggestionId) {
    // TODO: Navigate to AI suggestion details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening AI suggestion: $suggestionId')),
    );
  }

  void _handleSeeAllPress(String section) {
    // TODO: Navigate to see all screen for the specific section
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('See all $section')),
    );
  }
}
