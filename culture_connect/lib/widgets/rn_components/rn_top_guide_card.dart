import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native TopGuideCard replica
/// Horizontal scrolling card for Top Guides section
/// Matches reference design exactly with centered layout and proper styling
class RNTopGuideCard extends StatelessWidget {
  final String name;
  final String specialty;
  final String location; // Added location field
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String experience;
  final bool isCertified; // Added certification status
  final VoidCallback onPressed;
  final VoidCallback? onFavoritePressed;
  final bool isFavorite;

  const RNTopGuideCard({
    super.key,
    required this.name,
    required this.specialty,
    required this.location, // Added location parameter
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.experience,
    this.isCertified = true, // Default to certified
    required this.onPressed,
    this.onFavoritePressed,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 200, // Maintain current width
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              color: AppTheme.shadowColor,
              offset: Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center, // Center all content
          children: [
            // Guide Image Section with Wishlist Badge
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: Container(
                    height:
                        120, // Final optimization to completely resolve overflow while maintaining visual impact
                    width: double.infinity,
                    color: AppTheme.backgroundSecondary,
                    child: imageUrl.isNotEmpty
                        ? Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: AppTheme.backgroundSecondary,
                                child: const Icon(
                                  Icons.person_outlined,
                                  size: 48,
                                  color: AppTheme.textSecondaryColor,
                                ),
                              );
                            },
                          )
                        : const Icon(
                            Icons.person_outlined,
                            size: 48,
                            color: AppTheme.textSecondaryColor,
                          ),
                  ),
                ),

                // Wishlist Badge (Teal Shield Icon - Reference Design)
                if (onFavoritePressed != null)
                  Positioned(
                    top: 12,
                    right: 12,
                    child: GestureDetector(
                      onTap: onFavoritePressed,
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: AppTheme.secondaryColor, // Teal color
                          shape: BoxShape.circle,
                          boxShadow: const [
                            BoxShadow(
                              color: AppTheme.shadowColor,
                              offset: Offset(0, 2),
                              blurRadius: 4,
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                        child: Icon(
                          isFavorite ? Icons.shield : Icons.shield_outlined,
                          size: 18,
                          color: AppTheme.white,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // Guide Info Section - Pixel-perfect reference design match
            Padding(
              padding: const EdgeInsets.all(
                  16), // Increased padding for generous spacing like reference design (was 12px)
              child: Column(
                crossAxisAlignment:
                    CrossAxisAlignment.center, // Center all content
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Guide Name (Large, Bold, Centered)
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize:
                          AppTheme.fontSizeLg, // 18px - Larger for prominence
                      fontWeight: AppTheme.fontWeightBold,
                      color: AppTheme.textPrimaryColor,
                      height: 1.1, // Reduced from 1.2 for compactness
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(
                      height:
                          6), // Increased spacing for reference design match (was 4px)

                  // Location with Pin Icon (Centered)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.location_on_outlined,
                        size: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          location,
                          style: const TextStyle(
                            fontSize: AppTheme.fontSizeSm, // 14px
                            color: AppTheme.textSecondaryColor,
                            height: 1.1, // Compact line height
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                      height:
                          8), // Increased spacing for reference design match (was 6px)

                  // Rating & Experience Row (Horizontal, Centered)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Rating with Star
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.star,
                            size: 16,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            rating.toStringAsFixed(1),
                            style: const TextStyle(
                              fontSize: AppTheme.fontSizeSm, // 14px
                              fontWeight: AppTheme.fontWeightBold,
                              color: AppTheme.textPrimaryColor,
                              height: 1.1, // Reduced from 1.2 for compactness
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                          width:
                              20), // Increased gap to match reference design (was 12px)

                      // Experience Badge (Pink/Coral)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 3), // Reduced padding for compactness
                        decoration: BoxDecoration(
                          color: const Color(
                              0xFFFF6B9D), // Pink/coral color from reference
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          experience,
                          style: const TextStyle(
                            fontSize: AppTheme.fontSizeXs, // 12px
                            fontWeight: AppTheme.fontWeightMedium,
                            color: AppTheme.white,
                            height: 1.0, // More compact line height
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                      height:
                          8), // Increased spacing for reference design match (was 6px)

                  // Specialty (Centered)
                  Text(
                    specialty,
                    style: const TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      color: AppTheme.textSecondaryColor,
                      height: 1.1, // Compact line height
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(
                      height:
                          8), // Increased spacing for reference design match (was 6px)

                  // Certification Badge (Teal, Centered)
                  if (isCertified)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 3), // Reduced padding for compactness
                      decoration: BoxDecoration(
                        color: AppTheme.secondaryColor, // Teal color
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.verified_outlined,
                            size: 12, // Reduced icon size for compactness
                            color: AppTheme.white,
                          ),
                          SizedBox(width: 3), // Reduced spacing
                          Text(
                            'Certified',
                            style: TextStyle(
                              fontSize: AppTheme.fontSizeXs, // 12px
                              fontWeight: AppTheme.fontWeightMedium,
                              color: AppTheme.white,
                              height: 1.0, // More compact line height
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
