import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native TopGuideCard replica
/// Horizontal scrolling card for Top Guides section
class RNTopGuideCard extends StatelessWidget {
  final String name;
  final String specialty;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String experience;
  final VoidCallback onPressed;
  final VoidCallback? onFavoritePressed;
  final bool isFavorite;

  const RNTopGuideCard({
    super.key,
    required this.name,
    required this.specialty,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.experience,
    required this.onPressed,
    this.onFavoritePressed,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 200, // Slightly narrower for guide cards
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              color: AppTheme.shadowColor,
              offset: Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Guide Image Section
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: Container(
                    height: 120, // Compact height for guide photo
                    width: double.infinity,
                    color: AppTheme.backgroundSecondary,
                    child: imageUrl.isNotEmpty
                        ? Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: AppTheme.backgroundSecondary,
                                child: const Icon(
                                  Icons.person_outlined,
                                  size: 48,
                                  color: AppTheme.textSecondaryColor,
                                ),
                              );
                            },
                          )
                        : const Icon(
                            Icons.person_outlined,
                            size: 48,
                            color: AppTheme.textSecondaryColor,
                          ),
                  ),
                ),

                // Favorite Button
                if (onFavoritePressed != null)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: onFavoritePressed,
                      child: Container(
                        width: 28,
                        height: 28,
                        decoration: BoxDecoration(
                          color: AppTheme.white.withAlpha(230),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isFavorite ? Icons.favorite : Icons.favorite_border,
                          size: 16,
                          color: isFavorite
                              ? Colors.red
                              : AppTheme.textSecondaryColor,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // Guide Info Section - Optimized spacing to prevent overflow
            Padding(
              padding: const EdgeInsets.all(10), // Reduced from 12px to 10px
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min, // Prevent expansion
                children: [
                  // Guide Name
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: AppTheme.fontSizeMd, // 16px
                      fontWeight: AppTheme.fontWeightBold,
                      color: AppTheme.textPrimaryColor,
                      height: 1.2, // Compact line height
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 3), // Reduced from 4px

                  // Specialty
                  Text(
                    specialty,
                    style: const TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      color: AppTheme.textSecondaryColor,
                      height: 1.2, // Compact line height
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 5), // Reduced from 6px

                  // Experience
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8, vertical: 3), // Reduced vertical padding
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withAlpha(26), // 10% opacity
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      experience,
                      style: const TextStyle(
                        fontSize: AppTheme.fontSizeXs, // 12px
                        fontWeight: AppTheme.fontWeightMedium,
                        color: AppTheme.primaryColor,
                        height: 1.1, // Very compact line height
                      ),
                    ),
                  ),
                  const SizedBox(height: 6), // Reduced from 8px

                  // Rating
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 14,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        rating.toStringAsFixed(1),
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeXs, // 12px
                          fontWeight: AppTheme.fontWeightSemibold,
                          color: AppTheme.textPrimaryColor,
                          height: 1.1, // Compact line height
                        ),
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        // Prevent overflow in rating text
                        child: Text(
                          '($reviewCount)',
                          style: const TextStyle(
                            fontSize: AppTheme.fontSizeXs, // 12px
                            color: AppTheme.textSecondaryColor,
                            height: 1.1, // Compact line height
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
