flutter: #24     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #25     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #34     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:625:14)
flutter: #35     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #42     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #43     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #44     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #52     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #53     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #54     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #66     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #67     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #68     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #69     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #70     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #71     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #72     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #74     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #85     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #86     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #87     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #88     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #89     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #90     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #91     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #92     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #93     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #94     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #95     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #96     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #103    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #107    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #108    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #109    _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #110    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #111    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #112    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #113    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #114    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #115    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #120    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #121    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #129    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #130    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #134    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #135    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #142    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #150    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #151    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #152    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #156    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #157    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #158    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #159    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #163    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #170    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #171    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #172    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #173    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #174    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #175    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #176    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #177    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #178    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #181    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #182    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #183    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #184    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #185    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #186    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #187    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #188    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #189    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #190    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #191    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #192    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #193    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #194    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #195    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #196    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #197    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #202    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #203    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #204    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #205    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #206    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #207    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #208    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #209    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #210    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #211    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #212    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #213    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #214    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #215    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #216    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #217    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #218    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #219    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #220    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #221    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #222    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #223    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #224    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #225    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #226    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #227    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #228    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #229    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #230    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #231    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #232    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #233    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #234    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #235    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #236    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #243    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #244    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #245    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #246    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #247    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #248    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #249    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #250    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #251    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #252    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #253    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #254    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #255    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #256    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #257    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #258    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #259    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #260    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #261    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #262    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #263    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #264    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #265    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #266    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #267    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #268    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #269    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #270    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #271    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #272    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #273    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #274    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #275    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #276    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #277    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #278    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #279    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #280    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #281    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #282    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #283    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #284    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #285    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #286    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #287    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #288    _invoke (dart:ui/hooks.dart:312:13)
flutter: #289    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #290    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T16:57:18.466962] [FlutterError] A RenderFlex overflowed by 28 pixels on the bottom. A RenderFlex overflowed by 28 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #19     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #20     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #21     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #22     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #23     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #24     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #25     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #34     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:625:14)
flutter: #35     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #42     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #43     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #44     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #52     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #53     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #54     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #66     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #67     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #68     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #69     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #70     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #71     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #72     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #74     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #85     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #86     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #87     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #88     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #89     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #90     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #91     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #92     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #93     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #94     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #95     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #96     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #103    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #107    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #108    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #109    _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #110    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #111    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #112    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #113    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #114    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #115    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #120    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #121    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #129    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #130    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #134    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #135    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #142    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #150    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #151    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #152    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #156    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #157    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #158    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #159    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #163    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #170    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #171    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #172    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #173    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #174    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #175    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #176    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #177    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #178    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #181    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #182    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #183    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #184    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #185    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #186    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #187    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #188    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #189    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #190    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #191    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #192    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #193    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #194    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #195    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #196    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #197    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #202    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #203    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #204    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #205    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #206    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #207    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #208    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #209    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #210    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #211    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #212    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #213    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #214    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #215    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #216    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #217    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #218    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #219    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #220    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #221    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #222    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #223    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #224    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #225    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #226    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #227    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #228    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #229    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #230    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #231    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #232    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #233    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #234    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #235    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #236    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #243    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #244    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #245    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #246    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #247    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #248    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #249    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #250    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #251    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #252    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #253    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #254    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #255    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #256    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #257    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #258    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #259    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #260    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #261    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #262    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #263    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #264    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #265    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #266    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #267    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #268    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #269    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #270    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #271    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #272    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #273    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #274    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #275    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #276    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #277    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #278    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #279    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #280    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #281    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #282    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #283    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #284    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #285    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #286    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #287    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #288    _invoke (dart:ui/hooks.dart:312:13)
flutter: #289    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #290    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T16:57:18.469761] [FlutterError] A RenderFlex overflowed by 51 pixels on the bottom. A RenderFlex overflowed by 51 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #19     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #20     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #21     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #22     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #23     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #24     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #25     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #34     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:625:14)
flutter: #35     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #42     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #43     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #44     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #52     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #53     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #54     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #66     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #67     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #68     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #69     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #70     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #71     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #72     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #74     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #85     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #86     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #87     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #88     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #89     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #90     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #91     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #92     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #93     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #94     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #95     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #96     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #103    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #107    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #108    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #109    _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #110    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #111    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #112    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #113    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #114    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #115    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #120    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #121    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #129    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #130    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #134    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #135    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #142    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #150    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #151    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #152    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #156    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #157    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #158    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #159    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #163    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #170    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #171    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #172    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #173    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #174    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #175    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #176    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #177    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #178    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #181    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #182    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #183    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #184    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #185    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #186    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #187    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #188    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #189    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #190    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #191    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #192    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #193    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #194    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #195    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #196    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #197    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #202    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #203    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #204    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #205    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #206    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #207    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #208    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #209    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #210    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #211    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #212    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #213    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #214    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #215    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #216    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #217    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #218    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #219    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #220    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #221    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #222    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #223    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #224    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #225    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #226    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #227    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #228    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #229    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #230    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #231    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #232    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #233    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #234    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #235    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #236    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #243    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #244    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #245    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #246    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #247    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #248    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #249    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #250    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #251    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #252    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #253    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #254    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #255    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #256    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #257    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #258    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #259    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #260    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #261    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #262    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #263    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #264    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #265    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #266    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #267    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #268    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #269    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #270    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #271    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #272    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #273    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #274    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #275    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #276    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #277    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #278    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #279    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #280    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #281    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #282    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #283    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #284    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #285    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #286    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #287    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #288    _invoke (dart:ui/hooks.dart:312:13)
flutter: #289    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #290    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T16:57:18.519858] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-19T16:57:18.520659] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-19T16:57:18.521350] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-19T16:57:18.522230] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-19T16:57:18.526142] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-19T16:57:18.557718] [EnhancedOfflineModeService] Starting offline content sync
flutter: ❌ ERROR [2025-07-19T16:57:18.558166] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T16:57:18.559943] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T16:57:18.561318] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-19T16:57:18.561655] [OfflineModeService] Offline content sync completed
flutter: 🐛 DEBUG [2025-07-19T16:57:18.561918] [PerformanceMonitoringService] Slow frame detected {"duration_ms":330}
flutter: 🐛 DEBUG [2025-07-19T16:57:18.741809] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:19.111315] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-19T16:57:19.140484] [PerformanceMonitoringService] Slow frame detected {"duration_ms":29}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T16:57:19.532356] [PerformanceMonitoringService] Slow frame detected {"duration_ms":74}
flutter: 🐛 DEBUG [2025-07-19T16:57:19.557708] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-19T16:57:20.032950] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.057543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.090498] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.125974] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.157333] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.190555] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.224230] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.307291] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.459023] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.583194] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.609427] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.640541] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.675226] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:20.840884] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:21.636523] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-19T16:57:21.658209] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-19T16:57:21.833988] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T16:57:21.858004] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-19T16:57:21.924673] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:22.679153] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-19T16:57:22.708225] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-19T16:57:22.824337] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:23.728388] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-19T16:57:23.758246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-19T16:57:24.765349] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T16:57:24.791573] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-19T16:57:25.108461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-07-19T16:57:25.260658] [FlutterError] A RenderFlex overflowed by 11 pixels on the bottom. A RenderFlex overflowed by 11 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #19     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #20     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #21     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #22     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #23     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #24     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #25     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #34     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:625:14)
flutter: #35     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #39     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #40     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #41     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #42     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #43     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #44     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #45     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #46     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #47     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #52     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #53     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #54     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #66     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #67     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #68     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #69     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #70     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #71     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #72     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #73     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #74     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #75     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #76     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #77     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #78     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #79     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #80     _invoke (dart:ui/hooks.dart:312:13)
flutter: #81     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #82     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T16:57:25.797966] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-19T16:57:25.824893] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-19T16:57:26.832572] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-19T16:57:26.857391] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T16:57:27.860237] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: ⚠️ WARNING [2025-07-19T16:57:32.383442] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-07-19T16:57:43.008135] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T16:57:43.058305] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-07-19T16:57:47.384390] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
