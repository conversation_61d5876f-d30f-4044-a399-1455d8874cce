flutter: #81     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #82     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #83     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #84     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #85     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #86     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #87     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #88     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #89     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #90     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #91     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #92     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #93     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #94     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #95     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #96     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #97     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #98     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #99     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #100    _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #101    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #102    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #103    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #104    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #105    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #106    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #107    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #108    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #109    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #110    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #111    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #112    ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #113    RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #114    RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #115    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #116    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #117    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #118    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #119    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #120    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #121    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #122    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #123    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #124    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #125    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #126    RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #127    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #128    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #129    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #130    _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #131    _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #132    RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #133    PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #134    PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #135    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #136    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #137    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #138    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #139    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #140    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #141    _invoke (dart:ui/hooks.dart:312:13)
flutter: #142    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #143    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T13:37:13.961126] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-19T13:37:13.963221] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-19T13:37:13.965968] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-19T13:37:13.968033] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-19T13:37:13.978373] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-19T13:37:13.983121] [PerformanceMonitoringService] Slow frame detected {"duration_ms":866}
flutter: ❌ ERROR [2025-07-19T13:37:14.264212] [FlutterError] A RenderFlex overflowed by 61 pixels on the bottom. A RenderFlex overflowed by 61 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #74     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #85     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #86     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #87     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #112    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #113    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #120    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #129    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #130    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #137    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #141    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #145    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #146    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #147    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #148    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #149    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #156    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #165    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #166    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #170    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #171    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #172    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #173    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #174    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #175    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #181    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #182    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #188    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #189    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #196    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #197    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #207    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #208    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #209    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #210    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #211    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #215    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #216    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #217    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #218    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #219    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #220    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #221    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #222    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #223    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #224    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #225    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #226    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #227    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #228    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #229    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #230    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #236    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #244    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #245    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #246    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #247    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #248    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #249    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #250    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #251    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #252    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #253    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #254    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #255    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #256    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #257    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #258    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #259    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #260    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #261    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #262    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #263    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #264    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #265    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #266    _invoke (dart:ui/hooks.dart:312:13)
flutter: #267    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #268    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T13:37:14.266891] [FlutterError] A RenderFlex overflowed by 61 pixels on the bottom. A RenderFlex overflowed by 61 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #74     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #85     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #86     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #87     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #112    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #113    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #120    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #129    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #130    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #137    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #141    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #145    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #146    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #147    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #148    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #149    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #156    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #165    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #166    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #170    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #171    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #172    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #173    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #174    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #175    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #181    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #182    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #188    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #189    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #196    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #197    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #207    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #208    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #209    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #210    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #211    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #215    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #216    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #217    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #218    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #219    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #220    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #221    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #222    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #223    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #224    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #225    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #226    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #227    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #228    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #229    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #230    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #236    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #244    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #245    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #246    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #247    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #248    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #249    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #250    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #251    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #252    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #253    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #254    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #255    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #256    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #257    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #258    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #259    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #260    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #261    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #262    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #263    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #264    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #265    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #266    _invoke (dart:ui/hooks.dart:312:13)
flutter: #267    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #268    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T13:37:14.343920] [EnhancedOfflineModeService] Starting offline content sync
flutter: 🐛 DEBUG [2025-07-19T13:37:14.347213] [PerformanceMonitoringService] Slow frame detected {"duration_ms":363}
flutter: 🐛 DEBUG [2025-07-19T13:37:14.351226] [OfflineModeService] Offline content sync completed
flutter: ❌ ERROR [2025-07-19T13:37:14.352370] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T13:37:14.358427] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T13:37:14.363718] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-19T13:37:14.364721] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-19T13:37:14.393969] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:14.426343] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T13:37:14.560153] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:14.819484] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T13:37:14.851564] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.040968] [PerformanceMonitoringService] Slow frame detected {"duration_ms":48}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.061200] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.503504] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.529150] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.637392] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.660118] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.700543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.728555] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T13:37:15.886601] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-07-19T13:37:15.908956] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.209841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.475841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.626460] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.692465] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.725990] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.776412] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.829867] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.877123] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.950746] [PerformanceMonitoringService] Slow frame detected {"duration_ms":74}
flutter: 🐛 DEBUG [2025-07-19T13:37:16.976568] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.204100] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.242575] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.310927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":68}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.392429] [PerformanceMonitoringService] Slow frame detected {"duration_ms":81}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.442840] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.536519] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.577062] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.611284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.659732] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-07-19T13:37:17.699834] [PerformanceMonitoringService] High memory usage detected {"memory_mb":199.0}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.700727] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.759315] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.826129] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:17.892587] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.058958] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.109995] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.148946] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: 🐛 DEBUG [2025-07-19T13:37:18.299308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":149}
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-19T13:37:18.462853] [PerformanceMonitoringService] Slow frame detected {"duration_ms":144}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.509786] [PerformanceMonitoringService] Slow frame detected {"duration_ms":67}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.577115] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.636433] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.718674] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.759613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.846329] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.910397] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:18.959152] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:19.105955] [PerformanceMonitoringService] Slow frame detected {"duration_ms":63}
flutter: 🐛 DEBUG [2025-07-19T13:37:19.125724] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-19T13:37:20.012950] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-19T13:37:20.076707] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-19T13:37:20.137765] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T13:37:20.227568] [PerformanceMonitoringService] Slow frame detected {"duration_ms":89}
flutter: 🐛 DEBUG [2025-07-19T13:37:20.310001] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:20.376209] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:20.443024] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:20.510214] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.345433] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.427450] [PerformanceMonitoringService] Slow frame detected {"duration_ms":80}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.475987] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.560527] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.626576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.693210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.759482] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.826751] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.893135] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.953456] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T13:37:21.976116] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T13:37:22.142320] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:22.776213] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:22.843644] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:22.909773] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:22.976428] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.060863] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.143237] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.193631] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.276496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.359441] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.409618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.477064] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.542670] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.626448] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.692514] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.743468] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.809605] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.876494] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.943778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:23.992849] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-19T13:37:32.665288] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.060130] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.109866] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.159790] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.193293] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.228006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.260322] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.360838] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.410341] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.476590] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.526574] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.577676] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.609298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.642911] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.677399] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.759820] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.792575] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.843249] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.893399] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.926993] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:40.977398] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:41.010754] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:41.059696] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.377182] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.443626] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.476201] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.509839] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.543297] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.576335] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.609934] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.643327] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.693767] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.726224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.776645] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.809695] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.893223] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:42.943094] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.059964] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.143336] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.226338] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.343192] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.376704] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.476462] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.543189] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.610417] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.661545] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.864898] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.893749] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-19T13:37:43.976239] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ❌ ERROR [2025-07-19T13:37:43.979051] [FlutterError] A RenderFlex overflowed by 61 pixels on the bottom. A RenderFlex overflowed by 61 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #51     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #52     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #53     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #54     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #55     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #56     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #57     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #58     _invoke (dart:ui/hooks.dart:312:13)
flutter: #59     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #60     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T13:37:44.010029] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-07-19T13:37:44.036095] [FlutterError] A RenderFlex overflowed by 61 pixels on the bottom. A RenderFlex overflowed by 61 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #51     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #52     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #53     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #54     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #55     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #56     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #57     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #58     _invoke (dart:ui/hooks.dart:312:13)
flutter: #59     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #60     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T13:37:44.109715] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.176649] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.226362] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.259760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.296612] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.328128] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.376059] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.411083] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.444128] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.709672] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.743301] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.777410] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.858205] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.876269] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.943210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:44.994138] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.042882] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.079596] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.110171] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.143822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.177085] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.243519] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.293886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.326645] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.360210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.393128] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.442911] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.493390] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.543177] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.576451] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.609903] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.759542] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.793012] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.827533] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.860857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.909982] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.960607] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:45.993290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.061224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.093221] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.126884] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.160445] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.192982] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.493364] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.530540] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.576195] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.627370] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.676846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.710316] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.826810] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.860255] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.893331] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.942777] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:46.993370] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.076940] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.110307] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.143375] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.192759] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.226675] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.276136] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.309748] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.343419] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.410202] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.476799] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.527069] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.560] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.594113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.643899] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-07-19T13:37:47.665463] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.693962] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.726772] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.760043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.810102] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.843292] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.893414] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.943530] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:47.976512] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:48.009413] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:48.042670] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:48.076191] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:49.526298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:37:54.326158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:37:54.377089] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-07-19T13:38:02.664723] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
