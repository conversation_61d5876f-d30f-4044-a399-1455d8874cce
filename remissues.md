flutter: 🐛 DEBUG [2025-07-19T13:49:39.419651] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:49:39.486158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:39.519867] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:39.570829] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-07-19T13:49:39.818942] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:40.913916] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-19T13:49:40.940575] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.020029] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.086295] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.119665] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.169664] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.286704] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.336279] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.402635] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.455697] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.569704] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.636926] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.686656] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.802634] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:49:41.886705] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.386478] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.436127] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.469513] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.569879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.603811] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.636483] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.669001] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.953382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-19T13:49:42.986284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-07-19T13:49:43.019958] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:43.069607] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:49:43.169533] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:43.203875] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:43.236629] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:43.268925] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:43.353560] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:45.118593] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:49:45.137636] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-07-19T13:49:45.169255] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:45.219362] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:45.769367] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:47.335863] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:47.369683] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:47.420041] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:48.770276] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:48.819547] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:48.870140] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:49.754258] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:49.802963] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:49.836348] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.019782] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.053836] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.086037] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.152405] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.285948] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.371489] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.436929] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.470352] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.502449] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.536052] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.670050] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.770233] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:50.853896] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.169566] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.219416] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.270933] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.437229] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.503427] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.636207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.703598] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.786184] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ⚠️ WARNING [2025-07-19T13:49:51.894678] [PerformanceMonitoringService] High memory usage detected {"memory_mb":193.0}
flutter: 🐛 DEBUG [2025-07-19T13:49:51.903763] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.002818] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.605740] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.653092] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.703625] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.744323] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.770360] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.810053] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.853209] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.915625] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:52.935703] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:49:54.236576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:54.270131] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:54.337359] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:54.770430] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:54.836652] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:54.953912] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:55.003896] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:55.420458] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:55.486113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:55.604052] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:55.686586] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:49:55.736434] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:55.770191] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:55.803347] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.353757] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.403869] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.436989] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.536451] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.586846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.619061] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.687066] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.737231] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:56.769521] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:57.437130] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:49:57.502683] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.259391] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-07-19T13:50:00.386368] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.454181] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.487388] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.520192] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T13:50:00.553694] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.636377] [PerformanceMonitoringService] Slow frame detected {"duration_ms":82}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.669098] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.719354] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.753884] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.785782] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.819313] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.853345] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.903156] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:00.970380] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.036646] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.069707] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.120059] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.186786] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.220158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.269139] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.303456] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.354527] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.402545] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.438208] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.487158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.519886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.554340] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.603734] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.653594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.703185] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.736915] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.786945] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.836271] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.871035] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.920041] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:01.986443] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:02.020497] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: 🐛 DEBUG [2025-07-19T13:50:02.076332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: AutoLockService initialized with settings
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: ❌ ERROR [2025-07-19T13:50:02.520751] [FlutterError] type 'Null' is not a subtype of type 'String' type 'Null' is not a subtype of type 'String'
flutter: Stack trace:
flutter: #0      _RNHomeScreenState._buildTopGuidesSection.<anonymous closure> (package:culture_connect/screens/rn_home_screen.dart:710:28)
flutter: #1      SliverChildBuilderDelegate.build (package:flutter/src/widgets/scroll_delegate.dart:497:22)
flutter: #2      SliverMultiBoxAdaptorElement._build (package:flutter/src/widgets/sliver.dart:953:28)
flutter: #3      SliverMultiBoxAdaptorElement.createChild.<anonymous closure> (package:flutter/src/widgets/sliver.dart:967:55)
flutter: #4      BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3038:19)
flutter: #5      SliverMultiBoxAdaptorElement.createChild (package:flutter/src/widgets/sliver.dart:959:12)
flutter: #6      RenderSliverMultiBoxAdaptor._createOrObtainChild.<anonymous closure> (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:367:23)
flutter: #7      RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #8      PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #9      RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #10     RenderSliverMultiBoxAdaptor._createOrObtainChild (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:356:5)
flutter: #11     RenderSliverMultiBoxAdaptor.insertAndLayoutChild (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:504:5)
flutter: #12     RenderSliverList.performLayout.advance (package:flutter/src/rendering/sliver_list.dart:243:19)
flutter: #13     RenderSliverList.performLayout (package:flutter/src/rendering/sliver_list.dart:285:12)
flutter: #14     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #15     RenderSliverEdgeInsetsPadding.performLayout (package:flutter/src/rendering/sliver_padding.dart:124:12)
flutter: #16     RenderSliverPadding.performLayout (package:flutter/src/rendering/sliver_padding.dart:330:11)
flutter: #17     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #18     RenderViewportBase.layoutChildSequence (package:flutter/src/rendering/viewport.dart:609:13)
flutter: #19     RenderViewport._attemptLayout (package:flutter/src/rendering/viewport.dart:1524:12)
flutter: #20     RenderViewport.performLayout (package:flutter/src/rendering/viewport.dart:1435:20)
flutter: #21     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #22     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #23     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #24     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #25     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #26     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #27     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #28     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #29     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #30     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #31     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #32     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #33     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #34     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #37     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1055:73)
flutter: #38     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #39     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #40     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #41     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #42     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #43     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1055:73)
flutter: #44     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     _RenderSingleChildViewport.performLayout (package:flutter/src/widgets/single_child_scroll_view.dart:490:14)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #51     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #52     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #57     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #58     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #61     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #62     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #63     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #64     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #65     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #66     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #67     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #68     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #69     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #70     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #71     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #72     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #73     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #74     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #75     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #76     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #77     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #78     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #79     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #80     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #81     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #82     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #83     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #84     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #85     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #86     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #87     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #88     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #89     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #90     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #91     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #92     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #93     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #94     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #95     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #96     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #97     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #98     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #99     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #100    _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #101    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #102    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #103    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #104    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #105    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #106    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #107    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #108    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #109    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #110    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #111    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #112    ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #113    RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #114    RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #115    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #116    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #117    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #118    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #119    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #120    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #121    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #122    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #123    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #124    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #125    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #126    RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #127    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #128    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #129    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #130    _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #131    _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #132    RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #133    PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #134    PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #135    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #136    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #137    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #138    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #139    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #140    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #141    _invoke (dart:ui/hooks.dart:312:13)
flutter: #142    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #143    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T13:50:02.720193] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-19T13:50:02.721712] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-19T13:50:02.722880] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-19T13:50:02.724486] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-19T13:50:02.731705] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-19T13:50:02.735927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":660}
flutter: ❌ ERROR [2025-07-19T13:50:02.789085] [FlutterError] A RenderFlex overflowed by 12 pixels on the bottom. A RenderFlex overflowed by 12 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #74     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #85     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #86     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #87     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #112    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #113    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #120    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #129    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #130    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #137    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #141    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #145    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #146    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #147    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #148    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #149    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #156    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #165    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #166    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #170    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #171    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #172    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #173    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #174    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #175    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #181    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #182    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #188    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #189    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #196    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #197    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #207    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #208    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #209    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #210    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #211    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #215    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #216    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #217    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #218    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #219    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #220    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #221    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #222    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #223    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #224    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #225    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #226    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #227    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #228    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #229    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #230    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #236    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #244    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #245    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #246    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #247    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #248    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #249    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #250    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #251    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #252    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #253    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #254    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #255    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #256    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #257    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #258    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #259    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #260    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #261    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #262    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #263    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #264    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #265    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #266    _invoke (dart:ui/hooks.dart:312:13)
flutter: #267    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #268    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T13:50:02.797359] [FlutterError] A RenderFlex overflowed by 12 pixels on the bottom. A RenderFlex overflowed by 12 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #74     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #85     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #86     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #87     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #112    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #113    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #120    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #129    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #130    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #137    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #141    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #145    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #146    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #147    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #148    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #149    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #156    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #165    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #166    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #170    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #171    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #172    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #173    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #174    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #175    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #181    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #182    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #188    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #189    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #196    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #197    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #207    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #208    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #209    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #210    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #211    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #215    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #216    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #217    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #218    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #219    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #220    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #221    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #222    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #223    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #224    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #225    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #226    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #227    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #228    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #229    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #230    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #236    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #244    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #245    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #246    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #247    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #248    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #249    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #250    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #251    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #252    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #253    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #254    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #255    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #256    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #257    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #258    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #259    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #260    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #261    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #262    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #263    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #264    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #265    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #266    _invoke (dart:ui/hooks.dart:312:13)
flutter: #267    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #268    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T13:50:02.901789] [EnhancedOfflineModeService] Starting offline content sync
flutter: 🐛 DEBUG [2025-07-19T13:50:02.905579] [OfflineModeService] Offline content sync completed
flutter: ❌ ERROR [2025-07-19T13:50:02.906019] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T13:50:02.908080] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T13:50:02.909673] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-19T13:50:02.920389] [PerformanceMonitoringService] Slow frame detected {"duration_ms":183}
flutter: 🐛 DEBUG [2025-07-19T13:50:02.985785] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:03.288957] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-19T13:50:03.436310] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-19T13:50:03.628332] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-19T13:50:03.652478] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T13:50:03.919277] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T13:50:04.091500] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-19T13:50:04.119246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-19T13:50:04.436781] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:04.545313] [PerformanceMonitoringService] Slow frame detected {"duration_ms":88}
flutter: 🐛 DEBUG [2025-07-19T13:50:04.590889] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:04.712574] [PerformanceMonitoringService] Slow frame detected {"duration_ms":120}
flutter: 🐛 DEBUG [2025-07-19T13:50:04.802618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":90}
flutter: 🐛 DEBUG [2025-07-19T13:50:04.838426] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-19T13:50:04.930058] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-07-19T13:50:04.953641] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.007820] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.043496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.088649] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.536486] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.637405] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.703051] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.846738] [PerformanceMonitoringService] Slow frame detected {"duration_ms":141}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.921159] [PerformanceMonitoringService] Slow frame detected {"duration_ms":74}
flutter: 🐛 DEBUG [2025-07-19T13:50:05.955207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.019676] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.070816] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.103367] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.169164] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.238165] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.286308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.337458] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.402993] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.469613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.537471] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.587298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:06.670483] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: 🐛 DEBUG [2025-07-19T13:50:06.755219] [PerformanceMonitoringService] Slow frame detected {"duration_ms":89}
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-19T13:50:06.991523] [PerformanceMonitoringService] Slow frame detected {"duration_ms":237}
flutter: ⚠️ WARNING [2025-07-19T13:50:07.026777] [PerformanceMonitoringService] High memory usage detected {"memory_mb":176.0}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.056117] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.121161] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.153243] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.202899] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.269770] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.357652] [PerformanceMonitoringService] Slow frame detected {"duration_ms":88}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.386780] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.420456] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.486364] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.620395] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.686066] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.756235] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.813484] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.836172] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.870419] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.920774] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:07.969874] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.115082] [PerformanceMonitoringService] Slow frame detected {"duration_ms":141}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.174328] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.202671] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.287123] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.346758] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.437778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":89}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.503246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.570606] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.630272] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.703617] [PerformanceMonitoringService] Slow frame detected {"duration_ms":72}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.770192] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.837527] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.920028] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:50:08.969931] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:09.036302] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:09.103321] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:09.171294] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:09.236493] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:09.286050] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:09.353662] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:09.386438] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.069526] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.137264] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.186473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.270573] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.306995] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.370004] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.453627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.486480] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.554261] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.603164] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.653208] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.720021] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.787449] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.836801] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.903838] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:10.952865] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:11.020905] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:50:11.070022] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:11.120563] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:11.169888] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:50:11.337328] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T13:50:21.894539] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: 🐛 DEBUG [2025-07-19T13:50:29.636426] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:50:33.136671] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:50:34.667372] [PerformanceMonitoringService] Slow frame detected {"duration_ms":48}
flutter: 🐛 DEBUG [2025-07-19T13:50:34.687445] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: ⚠️ WARNING [2025-07-19T13:50:36.894115] [PerformanceMonitoringService] High memory usage detected {"memory_mb":193.0}
flutter: ⚠️ WARNING [2025-07-19T13:50:51.894260] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
