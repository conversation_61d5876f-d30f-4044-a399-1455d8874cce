flutter: 🐛 DEBUG [2025-07-19T08:52:03.832150] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.015033] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.082228] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.132108] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.381922] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.432209] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.464953] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.519683] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.549648] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.582044] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.631965] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.866308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.915556] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:04.948705] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.115910] [PerformanceMonitoringService] Slow frame detected {"duration_ms":67}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.165391] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.198607] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.448460] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.499251] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.534950] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.616170] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.681626] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.715571] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.765573] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.898433] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-19T08:52:05.948559] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.000121] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.365437] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.482117] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.688308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":90}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.765714] [PerformanceMonitoringService] Slow frame detected {"duration_ms":76}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.831559] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.899205] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.933205] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:06.969929] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-19T08:52:07.036723] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:07.065754] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-19T08:52:07.199122] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:07.248717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:07.365989] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:08.615857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:08.665735] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:08.715531] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:08.965931] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:08.999493] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.048817] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.232582] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.282687] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.315527] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.699736] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.748649] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.781739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.898622] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:09.968428] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:10.028533] [PerformanceMonitoringService] Slow frame detected {"duration_ms":63}
flutter: 🐛 DEBUG [2025-07-19T08:52:10.049096] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-19T08:52:10.082027] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:10.131829] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:10.961412] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-19T08:52:10.982253] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.015382] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.066225] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.211844] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.231736] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.265997] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.298666] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.415616] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.465707] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:11.498521] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:12.299481] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:12.348912] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:12.382009] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T08:52:12.438587] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🐛 DEBUG [2025-07-19T08:52:15.334080] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:15.616479] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:15.666879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-07-19T08:52:15.796298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":114}
flutter: 🐛 DEBUG [2025-07-19T08:52:15.899161] [PerformanceMonitoringService] Slow frame detected {"duration_ms":101}
flutter: 🐛 DEBUG [2025-07-19T08:52:15.932734] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-19T08:52:15.964999] [PerformanceMonitoringService] Slow frame detected {"duration_ms":31}
flutter: 🐛 DEBUG [2025-07-19T08:52:15.998635] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.165788] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.232799] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.282188] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.332640] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.382197] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.415744] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.448541] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.481773] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.515931] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.548670] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.581958] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.617021] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.649481] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.682364] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.716746] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.761039] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.781662] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.814927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.851760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.898649] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.932261] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.964985] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:16.999488] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.032305] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.065971] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.099391] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.148435] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.202750] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.232824] [PerformanceMonitoringService] Slow frame detected {"duration_ms":29}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.281698] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.315928] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.348926] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.382541] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.415948] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.449233] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.500037] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.532276] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.565245] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.599496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.631879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.666054] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.716167] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.749411] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.816310] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.849630] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.916150] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.948897] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:17.982627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: 🐛 DEBUG [2025-07-19T08:52:18.037258] [PerformanceMonitoringService] Slow frame detected {"duration_ms":55}
flutter: AutoLockService initialized with settings
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-07-19T08:52:18.870897] [PerformanceMonitoringService] Slow frame detected {"duration_ms":827}
flutter: ❌ ERROR [2025-07-19T08:52:18.980856] [FlutterError] A RenderFlex overflowed by 43 pixels on the bottom. A RenderFlex overflowed by 43 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T08:52:19.096959] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T08:52:19.101722] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T08:52:19.105100] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T08:52:19.108592] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T08:52:19.222881] [PerformanceMonitoringService] Slow frame detected {"duration_ms":350}
flutter: 🐛 DEBUG [2025-07-19T08:52:19.246751] [EnhancedOfflineModeService] Saved offline settings
flutter: 🐛 DEBUG [2025-07-19T08:52:19.247426] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-19T08:52:19.252442] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-19T08:52:19.260558] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-19T08:52:19.268001] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-19T08:52:19.292152] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-07-19T08:52:19.297664] [EnhancedOfflineModeService] Saved 1 sync schedules
flutter: 🐛 DEBUG [2025-07-19T08:52:19.297915] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-19T08:52:19.333033] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: ❌ ERROR [2025-07-19T08:52:19.376696] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T08:52:19.380026] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T08:52:19.383216] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-19T08:52:19.397745] [PerformanceMonitoringService] Slow frame detected {"duration_ms":69}
flutter: 🐛 DEBUG [2025-07-19T08:52:19.421042] [PerformanceMonitoringService] Slow frame detected {"duration_ms":30}
flutter: 🐛 DEBUG [2025-07-19T08:52:19.598663] [PerformanceMonitoringService] Slow frame detected {"duration_ms":183}
flutter: 🐛 DEBUG [2025-07-19T08:52:19.716317] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.184632] [PerformanceMonitoringService] Slow frame detected {"duration_ms":86}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.378687] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T08:52:20.400965] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.468140] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.520542] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.566466] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.632994] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.665592] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.698653] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:20.748908] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:21.287736] [PerformanceMonitoringService] Slow frame detected {"duration_ms":122}
flutter: 🐛 DEBUG [2025-07-19T08:52:21.315109] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: 🐛 DEBUG [2025-07-19T08:52:22.148475] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-19T08:52:22.266037] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-19T08:52:22.436005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":108}
flutter: ⚠️ WARNING [2025-07-19T08:52:22.456326] [PerformanceMonitoringService] High memory usage detected {"memory_mb":156.0}
flutter: 🐛 DEBUG [2025-07-19T08:52:22.503998] [PerformanceMonitoringService] Slow frame detected {"duration_ms":129}
flutter: 🐛 DEBUG [2025-07-19T08:52:22.599179] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-07-19T08:52:22.649856] [PerformanceMonitoringService] Slow frame detected {"duration_ms":51}
flutter: 🐛 DEBUG [2025-07-19T08:52:22.748747] [PerformanceMonitoringService] Slow frame detected {"duration_ms":98}
flutter: 🐛 DEBUG [2025-07-19T08:52:22.832530] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:22.881757] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T08:52:22.948791] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:23.454111] [PerformanceMonitoringService] Slow frame detected {"duration_ms":89}
flutter: 🐛 DEBUG [2025-07-19T08:52:23.481741] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.032680] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.098837] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.166360] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.199290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.266283] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.317019] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.399319] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.449758] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.583255] [PerformanceMonitoringService] Slow frame detected {"duration_ms":135}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.665999] [PerformanceMonitoringService] Slow frame detected {"duration_ms":81}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.732686] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.773245] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.832274] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.865745] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:24.932286] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.015547] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.121295] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.183222] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.232135] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.282305] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.332037] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.382303] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.448647] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.565225] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.642518] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.665407] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.699505] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.765158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T08:52:25.832490] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-19T08:52:27.431515] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
flutter: 🐛 DEBUG [2025-07-19T08:52:33.276824] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-07-19T08:52:33.298705] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: ⚠️ WARNING [2025-07-19T08:52:42.431392] [PerformanceMonitoringService] High memory usage detected {"memory_mb":181.0}
