Restarted application in 2,368ms.
flutter: 🔥 Waiting for Firebase initialization...
flutter: 🚀 Starting app initialization
flutter: ✅ SharedPreferences initialized in 23ms
flutter: ✅ Firebase core initialized in 37ms
flutter: ✅ Preloaded asset: assets/images/splash.png (18355 bytes)
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 36ms
flutter: ✅ App initialization completed in 51ms
flutter: ✅ Firebase initialization completed successfully
flutter: ✅ Firebase full features initialized in 104ms
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 114ms
flutter: ℹ️ INFO [2025-07-19T17:26:03.879938] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-07-19T17:26:03.885003] [LoggingService] Device info: {name: iPhone 15 Pro Max, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-07-19T17:26:03.885846] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ℹ️ INFO [2025-07-19T17:26:03.902359] [ErrorHandlingService] Error handling service initialized
flutter: ℹ️ INFO [2025-07-19T17:26:03.919980] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-07-19T17:26:03.935460] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-07-19T17:26:03.940830] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-07-19T17:26:03.929216"}}
flutter: ℹ️ INFO [2025-07-19T17:26:03.947569] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-07-19T17:26:03.948085] [App] All services initialized successfully
flutter: SplashVideoBackground: Video failed to load, using fallback
flutter: VideoBackground: Failed to load video - PlatformException(video_player, *** -[NSURL initFileURLWithPath:]: nil string parameter, null, null)
flutter: 🐛 DEBUG [2025-07-19T17:26:06.017948] [PerformanceMonitoringService] Slow frame detected {"duration_ms":153}
flutter: 🐛 DEBUG [2025-07-19T17:26:06.057627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T17:26:06.112746] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-19T17:26:06.152430] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-19T17:26:06.175867] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-07-19T17:26:06.473583] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:07.040127] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:09.671646] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T17:26:13.942903] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: 🐛 DEBUG [2025-07-19T17:26:14.236168] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:14.269290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:14.336143] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:14.569662] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:15.243548] [PerformanceMonitoringService] Slow frame detected {"duration_ms":300}
flutter: 🐛 DEBUG [2025-07-19T17:26:15.353263] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-19T17:26:17.030464] [PerformanceMonitoringService] Slow frame detected {"duration_ms":95}
flutter: 🐛 DEBUG [2025-07-19T17:26:17.068625] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-19T17:26:17.135978] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T17:26:17.168551] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:17.252319] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T17:26:19.042202] [PerformanceMonitoringService] High memory usage detected {"memory_mb":192.0}
flutter: 🐛 DEBUG [2025-07-19T17:26:19.068109] [PerformanceMonitoringService] Slow frame detected {"duration_ms":416}
flutter: 🐛 DEBUG [2025-07-19T17:26:19.184452] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-19T17:26:20.852934] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: 🐛 DEBUG [2025-07-19T17:26:20.925754] [PerformanceMonitoringService] Slow frame detected {"duration_ms":74}
flutter: 🐛 DEBUG [2025-07-19T17:26:20.967770] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T17:26:21.050685] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T17:26:21.083855] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:21.150725] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:22.822027] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:22.883575] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T17:26:22.966717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:23.666707] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:23.983708] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:24.166605] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:24.366693] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:25.000129] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:26.599844] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:27.982539] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:28.249217] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:28.616154] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:28.982063] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T17:26:29.099420] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:29.849362] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:32.115611] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:32.282259] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:37.564960] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:37.614752] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:39.614780] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-07-19T17:26:41.594111] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-19T17:26:41.648090] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: AutoLockService initialized with settings
flutter: Offline mode: Loading bookings from cache
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T17:26:43.702474] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-19T17:26:43.704006] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-19T17:26:43.705421] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-19T17:26:43.707334] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-19T17:26:43.715414] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-19T17:26:43.718831] [PerformanceMonitoringService] Slow frame detected {"duration_ms":600}
flutter: ❌ ERROR [2025-07-19T17:26:43.875119] [FlutterError] A RenderFlex overflowed by 5.0 pixels on the bottom. A RenderFlex overflowed by 5.0 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #13     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #41     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:625:14)
flutter: #42     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #45     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #46     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #51     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #52     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #54     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #58     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #59     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #60     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #61     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #62     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #63     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #64     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #65     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #66     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #67     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #68     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #69     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #70     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #71     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #72     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #73     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #74     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #75     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #76     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #77     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #78     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #79     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #80     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #81     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #90     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #91     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #103    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #107    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #108    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #109    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #110    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #114    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #115    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #116    _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #119    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #120    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #141    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #142    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #146    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #147    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #148    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #149    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #153    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #154    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #155    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #156    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #157    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #158    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #159    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #163    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #164    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #165    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #166    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #170    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #174    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #175    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #176    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #177    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #178    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #181    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #182    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #184    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #185    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #186    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #187    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #188    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #189    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #194    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #195    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #196    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #197    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #198    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #199    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #200    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #201    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #207    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #208    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #209    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #210    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #211    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #215    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #216    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #217    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #218    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #219    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #220    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #221    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #222    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #223    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #224    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #225    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #226    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #227    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #228    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #229    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #230    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #236    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #237    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #244    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #245    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #246    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #247    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #248    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #249    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #250    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #251    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #252    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #253    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #254    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #255    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #256    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #257    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #258    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #259    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #260    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #261    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #262    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #263    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #264    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #265    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #266    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #267    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #268    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #269    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #270    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #271    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #272    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #273    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #274    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #275    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #276    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #277    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #278    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #279    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #280    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #281    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #282    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #283    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #284    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #285    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #286    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #287    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #288    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #289    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #290    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #291    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #292    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #293    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #294    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #295    _invoke (dart:ui/hooks.dart:312:13)
flutter: #296    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #297    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T17:26:43.915623] [FlutterError] A RenderFlex overflowed by 21 pixels on the bottom. A RenderFlex overflowed by 21 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #13     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #41     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:625:14)
flutter: #42     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #43     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #44     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #45     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #46     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #47     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #48     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #49     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #50     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #51     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #52     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #53     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #54     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #55     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #56     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #57     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #58     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #59     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #60     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #61     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #62     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #63     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #64     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #65     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #66     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #67     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #68     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #69     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #70     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #71     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #72     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #73     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #74     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #75     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #76     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #77     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #78     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #79     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #80     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #81     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #90     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #91     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #103    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #104    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #105    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #106    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #107    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #108    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #109    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #110    RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #114    PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #115    PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #116    _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #119    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #120    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #141    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #142    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #146    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #147    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #148    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #149    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #153    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #154    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #155    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #156    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #157    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #158    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #159    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #163    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #164    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #165    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #166    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #170    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #174    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #175    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #176    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #177    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #178    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #181    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #182    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #184    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #185    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #186    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #187    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #188    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #189    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #194    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #195    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #196    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #197    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #198    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #199    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #200    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #201    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #207    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #208    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #209    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #210    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #211    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #215    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #216    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #217    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #218    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #219    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #220    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #221    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #222    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #223    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #224    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #225    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #226    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #227    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #228    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #229    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #230    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #236    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #237    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #244    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #245    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #246    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #247    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #248    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #249    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #250    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #251    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #252    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #253    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #254    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #255    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #256    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #257    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #258    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #259    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #260    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #261    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #262    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #263    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #264    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #265    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #266    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #267    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #268    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #269    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #270    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #271    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #272    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #273    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #274    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #275    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #276    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #277    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #278    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #279    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #280    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #281    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #282    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #283    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #284    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #285    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #286    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #287    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #288    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #289    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #290    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #291    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #292    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #293    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #294    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #295    _invoke (dart:ui/hooks.dart:312:13)
flutter: #296    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #297    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T17:26:44.035275] [EnhancedOfflineModeService] Starting offline content sync
flutter: 🐛 DEBUG [2025-07-19T17:26:44.035682] [PerformanceMonitoringService] Slow frame detected {"duration_ms":317}
flutter: ❌ ERROR [2025-07-19T17:26:44.046853] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T17:26:44.048661] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T17:26:44.050116] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-19T17:26:44.050540] [OfflineModeService] Offline content sync completed
flutter: 🐛 DEBUG [2025-07-19T17:26:44.305455] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T17:26:44.331039] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T17:26:44.843542] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-19T17:26:44.864444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T17:26:44.959588] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-19T17:26:44.980830] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-19T17:26:45.181997] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:45.996774] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T17:26:46.014073] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-19T17:26:47.047396] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T17:26:47.464026] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:47.914793] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T17:26:47.947404] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:48.014086] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:48.085412] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-19T17:26:48.131601] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-07-19T17:26:48.647494] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:49.481078] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-19T17:26:49.664327] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T17:26:49.763918] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:51.014045] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T17:26:54.364105] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T17:26:58.935890] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: ⚠️ WARNING [2025-07-19T17:27:13.936935] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: ⚠️ WARNING [2025-07-19T17:27:28.935902] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: ⚠️ WARNING [2025-07-19T17:27:43.936357] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}