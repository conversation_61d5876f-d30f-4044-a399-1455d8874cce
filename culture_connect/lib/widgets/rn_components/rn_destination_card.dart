import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native DestinationCard replica
/// Matches the exact styling from React Native reference images
class RNDestinationCard extends StatelessWidget {
  final String title;
  final String location;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final String price;
  final String duration;
  final List<String> tags;
  final VoidCallback onPressed;
  final VoidCallback? onFavoritePressed;
  final bool isFavorite;

  const RNDestinationCard({
    super.key,
    required this.title,
    required this.location,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.price,
    required this.duration,
    required this.tags,
    required this.onPressed,
    this.onFavoritePressed,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        constraints: const BoxConstraints(
          maxWidth: 600, // Prevent cards from becoming too wide on tablets
        ),
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              color: AppTheme.shadowColor,
              offset: Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Background Image (Full Card)
              Container(
                height: 280, // Increased height for better proportions
                width: double.infinity,
                color: AppTheme.backgroundSecondary,
                child: imageUrl.isNotEmpty
                    ? Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppTheme.backgroundSecondary,
                            child: const Icon(
                              Icons.image_outlined,
                              size: 48,
                              color: AppTheme.textSecondaryColor,
                            ),
                          );
                        },
                      )
                    : const Icon(
                        Icons.image_outlined,
                        size: 48,
                        color: AppTheme.textSecondaryColor,
                      ),
              ),

              // Category Badge - Top Left (matching reference image)
              if (tags.isNotEmpty)
                Positioned(
                  top: 16,
                  left: 16,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.secondaryColor, // Teal color
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      tags.first, // Use first tag as category
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: AppTheme.fontWeightSemibold,
                        color: AppTheme.white,
                      ),
                    ),
                  ),
                ),

              // Wishlist Heart - Top Right (matching reference image)
              if (onFavoritePressed != null)
                Positioned(
                  top: 16,
                  right: 16,
                  child: GestureDetector(
                    onTap: onFavoritePressed,
                    child: Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: AppTheme.white.withAlpha(230), // 90% opacity
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        size: 20,
                        color: isFavorite
                            ? Colors.red
                            : AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                ),

              // Content Overlay - Bottom (matching reference image)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        AppTheme.black.withAlpha(179), // 70% opacity
                        AppTheme.black.withAlpha(204), // 80% opacity at bottom
                      ],
                      stops: const [0.0, 0.6, 1.0],
                    ),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 20, // Larger for prominence
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),

                      // Location
                      Text(
                        location,
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeSm, // 14px
                          color: AppTheme.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),

                      // Price
                      Text(
                        price,
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeLg, // 18px
                          fontWeight: AppTheme.fontWeightBold,
                          color: AppTheme.white,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Amenities Row (matching reference image)
                      Row(
                        children: [
                          _buildAmenityItem(Icons.people_outline, '3 Guests'),
                          const SizedBox(width: 16),
                          _buildAmenityItem(Icons.bed_outlined, '2 Beds'),
                          const SizedBox(width: 16),
                          _buildAmenityItem(Icons.bathtub_outlined, '2 Baths'),
                          const SizedBox(width: 16),
                          _buildAmenityItem(
                              Icons.square_foot_outlined, '850 m²'),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Rating Badge - Bottom Right (matching reference image)
              Positioned(
                bottom: 16,
                right: 16,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.white.withAlpha(230), // 90% opacity
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.star,
                        size: 12,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        rating.toStringAsFixed(1),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Helper method to build amenity items (matching reference image)
  Widget _buildAmenityItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: AppTheme.white,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: const TextStyle(
            fontSize: 12,
            color: AppTheme.white,
          ),
        ),
      ],
    );
  }
}
