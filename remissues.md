Performing hot restart...                                               
Restarted application in 3,666ms.
flutter: 🔥 Waiting for Firebase initialization...
flutter: 🚀 Starting app initialization
flutter: ✅ SharedPreferences initialized in 28ms
flutter: ✅ Firebase core initialized in 36ms
flutter: ✅ Preloaded asset: assets/images/splash.png (18355 bytes)
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 34ms
flutter: ✅ App initialization completed in 55ms
flutter: ✅ Firebase initialization completed successfully
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Firebase full features initialized in 102ms
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 108ms
flutter: ℹ️ INFO [2025-07-19T13:58:49.133011] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-07-19T13:58:49.137652] [LoggingService] Device info: {name: iPhone 15 Pro Max, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-07-19T13:58:49.138519] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ℹ️ INFO [2025-07-19T13:58:49.155515] [ErrorHandlingService] Error handling service initialized
flutter: ℹ️ INFO [2025-07-19T13:58:49.168507] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-07-19T13:58:49.183174] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-07-19T13:58:49.190112] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-07-19T13:58:49.175156"}}
flutter: ℹ️ INFO [2025-07-19T13:58:49.195554] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-07-19T13:58:49.196276] [App] All services initialized successfully
flutter: ⚠️ WARNING [2025-07-19T13:58:49.204854] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: SplashVideoBackground: Video failed to load, using fallback
flutter: VideoBackground: Failed to load video - PlatformException(video_player, *** -[NSURL initFileURLWithPath:]: nil string parameter, null, null)
flutter: 🐛 DEBUG [2025-07-19T13:58:50.893439] [PerformanceMonitoringService] Slow frame detected {"duration_ms":142}
flutter: 🐛 DEBUG [2025-07-19T13:58:50.927277] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-19T13:58:50.959644] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.010144] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.064037] [PerformanceMonitoringService] Slow frame detected {"duration_ms":54}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.093205] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.147945] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.176917] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.228857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.259906] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.293412] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.345036] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.409978] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.463273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.493489] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.527102] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.560125] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.593458] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.627673] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.666183] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.694953] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.760951] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.830925] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.867848] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.893430] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.926877] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.959955] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:51.993532] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.036369] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.062116] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.094874] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.130576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.198172] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.269175] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.328694] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.379979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.445922] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.511276] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.543653] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.610120] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.643499] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.726416] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.762061] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.826885] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.859965] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.895831] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.926558] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:52.979558] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.009736] [PerformanceMonitoringService] Slow frame detected {"duration_ms":29}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.045062] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.100461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.126913] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.160567] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.235287] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.261333] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.295068] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.344259] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.377410] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.429755] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.480556] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.526860] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.577798] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.626640] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.664619] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.710287] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.744867] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.800307] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.849325] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.878254] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.928025] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:53.962755] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.009863] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.043173] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.078237] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.129181] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.161324] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.193196] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.226880] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.260927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.296304] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.363970] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.461132] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.560343] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.611116] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.643613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.676780] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.726890] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.760477] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.810440] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.843415] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.894241] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.927872] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:54.977231] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.010959] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.043872] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.093508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.127155] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.176865] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.210760] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.243165] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.277598] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.326391] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.360505] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.393874] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.443681] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.493416] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.526658] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.577067] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.627064] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.677037] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.710684] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.761069] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.793419] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.843605] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.877216] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.927005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:55.960492] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.011042] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.043912] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.094031] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.126736] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.176910] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.210944] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.252372] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.276396] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.311132] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.361005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.395257] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.443279] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.477044] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.543519] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.580508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.610951] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.659616] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.745326] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.827018] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.860284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.910248] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.943829] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:56.994021] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.026689] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.076408] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.109944] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.143666] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.177011] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.211138] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.260588] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.294388] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.329152] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.362846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.410330] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.444613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.477173] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.527001] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.576862] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.610056] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.661361] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.693937] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.744179] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.777675] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.826587] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.877352] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.910556] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.943490] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:57.994896] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.044025] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.076529] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.110387] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.142922] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.193394] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.227265] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.262669] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.310767] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.344942] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.393437] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.427104] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.460788] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.510375] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.543782] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.577600] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.627301] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.661063] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.710363] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.743675] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.793892] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.827731] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.860794] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.910184] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.943739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:58.993293] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.027834] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.061] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.109819] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.144516] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.177674] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.227910] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.260602] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.310103] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.344101] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.393764] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.427804] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.471060] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.493466] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.543307] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.579552] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.610364] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.711362] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.761306] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.794830] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.860566] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.911328] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.943270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:58:59.993733] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:00.027647] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:00.077527] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:00.148976] [PerformanceMonitoringService] Slow frame detected {"duration_ms":72}
flutter: 🐛 DEBUG [2025-07-19T13:59:00.522859] [PerformanceMonitoringService] Slow frame detected {"duration_ms":360}
flutter: 🐛 DEBUG [2025-07-19T13:59:00.676677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: 🐛 DEBUG [2025-07-19T13:59:00.911023] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.121626] [PerformanceMonitoringService] Slow frame detected {"duration_ms":145}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.193355] [PerformanceMonitoringService] Slow frame detected {"duration_ms":71}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.277092] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.310273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.360640] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.428260] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.478473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.527009] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.594285] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.627420] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.660140] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:04.693570] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:05.693743] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:05.977930] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:06.560694] [PerformanceMonitoringService] Slow frame detected {"duration_ms":566}
flutter: 🐛 DEBUG [2025-07-19T13:59:06.696291] [PerformanceMonitoringService] Slow frame detected {"duration_ms":136}
flutter: 🐛 DEBUG [2025-07-19T13:59:06.744187] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:06.777186] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.135463] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.159857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.287218] [PerformanceMonitoringService] Slow frame detected {"duration_ms":109}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.459841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":173}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.493338] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.526863] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.560308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.609716] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.666287] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-19T13:59:08.693282] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-19T13:59:10.279168] [PerformanceMonitoringService] Slow frame detected {"duration_ms":52}
flutter: 🐛 DEBUG [2025-07-19T13:59:10.409964] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-07-19T13:59:10.480994] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:10.577183] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-19T13:59:10.643773] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:10.677065] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:10.794329] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:11.010857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:11.110826] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:11.259855] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:11.310043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:11.843236] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:11.895166] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:11.994476] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.061290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.377318] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.410361] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.460419] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.493778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.624230] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.693370] [PerformanceMonitoringService] Slow frame detected {"duration_ms":68}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.744678] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:12.777841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:13.961142] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:14.059951] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:15.511171] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:15.560738] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:15.594222] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:15.835381] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-19T13:59:15.860311] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T13:59:15.894375] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:15.943788] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:16.245095] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:16.278003] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:16.327162] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:16.460461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:16.510841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:16.578240] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:16.760537] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:16.843444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.111471] [PerformanceMonitoringService] Slow frame detected {"duration_ms":51}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.143581] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.210038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.361352] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.411627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.443806] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.543740] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.610493] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.660118] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.727713] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.827353] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.894128] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:17.927465] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T13:59:19.195476] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.377390] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.410058] [PerformanceMonitoringService] Slow frame detected {"duration_ms":32}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.460707] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.544753] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.577122] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.610778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.643800] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.818936] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.825055] [PerformanceMonitoringService] Slow frame detected {"duration_ms":48}
flutter: 🐛 DEBUG [2025-07-19T13:59:20.844208] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: 🐛 DEBUG [2025-07-19T13:59:26.977083] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:27.027811] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:27.078627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:27.427548] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:27.478011] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:27.511545] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:27.644984] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:27.694231] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:27.728006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:28.511435] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:28.577224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:28.661964] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:28.693627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:28.729360] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:28.777345] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:28.811207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:28.844122] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:29.411793] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:29.460824] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:29.660849] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:29.710807] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:29.743408] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:29.827829] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:29.861556] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:29.893886] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:30.544926] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:30.594085] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:30.628212] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.628045] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.661282] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.710179] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.760649] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.793701] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.844394] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.877307] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.910570] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:33.961946] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.012112] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.061372] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T13:59:34.195626] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.394939] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.428868] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.493640] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.527007] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.627660] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.677038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.727392] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.761759] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.794728] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:34.861540] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-07-19T13:59:35.469668] [PerformanceMonitoringService] Slow frame detected {"duration_ms":92}
flutter: 🐛 DEBUG [2025-07-19T13:59:35.543573] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-07-19T13:59:35.579054] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T13:59:35.634752] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:35.718851] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:35.760123] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:35.844084] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:35.878556] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:35.928205] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:35.977772] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.028118] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.078010] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.128794] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.177191] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.244214] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.294344] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.361241] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.427650] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.477618] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.512024] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.560813] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.610658] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.660614] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.693654] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.726994] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.761126] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.794503] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.844326] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.878612] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.927620] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:36.977171] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.027389] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.078224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.128416] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.195136] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.228805] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.278676] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.328703] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.361271] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:37.411689] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: 🐛 DEBUG [2025-07-19T13:59:37.460307] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: AutoLockService initialized with settings
flutter: Offline mode: Loading bookings from cache
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: ❌ ERROR [2025-07-19T13:59:37.844108] [FlutterError] type 'Null' is not a subtype of type 'String' type 'Null' is not a subtype of type 'String'
flutter: Stack trace:
flutter: #0      _RNHomeScreenState._buildTopGuidesSection.<anonymous closure> (package:culture_connect/screens/rn_home_screen.dart:711:28)
flutter: #1      SliverChildBuilderDelegate.build (package:flutter/src/widgets/scroll_delegate.dart:497:22)
flutter: #2      SliverMultiBoxAdaptorElement._build (package:flutter/src/widgets/sliver.dart:953:28)
flutter: #3      SliverMultiBoxAdaptorElement.createChild.<anonymous closure> (package:flutter/src/widgets/sliver.dart:967:55)
flutter: #4      BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3038:19)
flutter: #5      SliverMultiBoxAdaptorElement.createChild (package:flutter/src/widgets/sliver.dart:959:12)
flutter: #6      RenderSliverMultiBoxAdaptor._createOrObtainChild.<anonymous closure> (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:367:23)
flutter: #7      RenderObject.invokeLayoutCallback.<anonymous closure> (package:flutter/src/rendering/object.dart:2738:59)
flutter: #8      PipelineOwner._enableMutationsToDirtySubtrees (package:flutter/src/rendering/object.dart:1108:15)
flutter: #9      RenderObject.invokeLayoutCallback (package:flutter/src/rendering/object.dart:2738:14)
flutter: #10     RenderSliverMultiBoxAdaptor._createOrObtainChild (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:356:5)
flutter: #11     RenderSliverMultiBoxAdaptor.insertAndLayoutChild (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:504:5)
flutter: #12     RenderSliverList.performLayout.advance (package:flutter/src/rendering/sliver_list.dart:243:19)
flutter: #13     RenderSliverList.performLayout (package:flutter/src/rendering/sliver_list.dart:285:12)
flutter: #14     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #15     RenderSliverEdgeInsetsPadding.performLayout (package:flutter/src/rendering/sliver_padding.dart:124:12)
flutter: #16     RenderSliverPadding.performLayout (package:flutter/src/rendering/sliver_padding.dart:330:11)
flutter: #17     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #18     RenderViewportBase.layoutChildSequence (package:flutter/src/rendering/viewport.dart:609:13)
flutter: #19     RenderViewport._attemptLayout (package:flutter/src/rendering/viewport.dart:1524:12)
flutter: #20     RenderViewport.performLayout (package:flutter/src/rendering/viewport.dart:1435:20)
flutter: #21     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #22     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #23     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #24     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #25     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #26     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #27     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #28     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #29     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #30     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #31     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #32     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #33     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #34     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #36     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #37     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1055:73)
flutter: #38     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #39     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #40     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #41     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #42     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #43     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1055:73)
flutter: #44     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1121:32)
flutter: #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #46     _RenderSingleChildViewport.performLayout (package:flutter/src/widgets/single_child_scroll_view.dart:490:14)
flutter: #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #50     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #51     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #52     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #54     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #56     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #57     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #58     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #60     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #61     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #62     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #63     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #64     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #65     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #66     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #67     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #68     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #69     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #70     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #71     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #72     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #73     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #74     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #75     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #76     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #77     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #78     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #79     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #80     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #81     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #82     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #83     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #84     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:297:14)
flutter: #85     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #86     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #87     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #88     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #89     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #90     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:239:12)
flutter: #91     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #92     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:178:12)
flutter: #93     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1105:7)
flutter: #94     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:242:7)
flutter: #95     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:409:14)
flutter: #96     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #97     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #98     RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #99     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #100    _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1476:11)
flutter: #101    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #102    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #103    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #104    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #105    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #106    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #107    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #108    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #109    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #110    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #111    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #112    ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:61:11)
flutter: #113    RenderStack._computeSize (package:flutter/src/rendering/stack.dart:601:43)
flutter: #114    RenderStack.performLayout (package:flutter/src/rendering/stack.dart:628:12)
flutter: #115    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #116    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #117    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #118    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #119    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #120    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #121    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #122    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #123    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #124    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #125    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #126    RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3720:14)
flutter: #127    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #128    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:117:21)
flutter: #129    RenderObject.layout (package:flutter/src/rendering/object.dart:2627:7)
flutter: #130    _RenderTheaterMixin.layoutChild (package:flutter/src/widgets/overlay.dart:1016:13)
flutter: #131    _RenderTheater.performLayout (package:flutter/src/widgets/overlay.dart:1328:9)
flutter: #132    RenderObject._layoutWithoutResize (package:flutter/src/rendering/object.dart:2465:7)
flutter: #133    PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1062:18)
flutter: #134    PipelineOwner.flushLayout (package:flutter/src/rendering/object.dart:1075:15)
flutter: #135    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:609:23)
flutter: #136    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #137    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #138    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #139    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #140    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #141    _invoke (dart:ui/hooks.dart:312:13)
flutter: #142    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #143    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T13:59:38.053493] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-19T13:59:38.055345] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-19T13:59:38.056983] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-19T13:59:38.058577] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-19T13:59:38.066161] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-19T13:59:38.070076] [PerformanceMonitoringService] Slow frame detected {"duration_ms":599}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T13:59:38.366066] [EnhancedOfflineModeService] Starting offline content sync
flutter: 🐛 DEBUG [2025-07-19T13:59:38.366430] [PerformanceMonitoringService] Slow frame detected {"duration_ms":300}
flutter: ❌ ERROR [2025-07-19T13:59:38.371705] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T13:59:38.377528] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T13:59:38.382727] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-19T13:59:38.384252] [OfflineModeService] Offline content sync completed
flutter: 🐛 DEBUG [2025-07-19T13:59:38.444347] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:38.571721] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-07-19T13:59:38.593774] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T13:59:38.990290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":63}
flutter: 🐛 DEBUG [2025-07-19T13:59:39.010677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-19T13:59:39.110365] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:39.179322] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-19T13:59:39.361299] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:39.394001] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:39.545042] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-19T13:59:40.277270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.327095] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.377216] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.494134] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.553761] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.593796] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.634380] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.720097] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.777243] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.844576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:40.914389] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.012446] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.077322] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.128534] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.187131] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.261653] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.310239] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.394347] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: 🐛 DEBUG [2025-07-19T13:59:41.512119] [PerformanceMonitoringService] Slow frame detected {"duration_ms":118}
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-19T13:59:41.628614] [PerformanceMonitoringService] Slow frame detected {"duration_ms":114}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.715560] [PerformanceMonitoringService] Slow frame detected {"duration_ms":88}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.761787] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.810290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.872888] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-19T13:59:41.944808] [PerformanceMonitoringService] Slow frame detected {"duration_ms":70}
flutter: 🐛 DEBUG [2025-07-19T13:59:42.028835] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:42.093935] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:42.160831] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:42.228383] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:42.333072] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:42.360379] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.177693] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.227367] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.311122] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.377295] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.427256] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.494666] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.560906] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.627455] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.693887] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.761243] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.827756] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.894613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:43.977866] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.044438] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.111204] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.177079] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: ⚠️ WARNING [2025-07-19T13:59:44.207376] [PerformanceMonitoringService] High memory usage detected {"memory_mb":156.0}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.244798] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.310948] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.377780] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.443695] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.510451] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.577369] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.660806] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.712003] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.777905] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.828018] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.910915] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:44.977616] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.014434] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.094609] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.178857] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.211622] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.260664] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.328210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.376867] [PerformanceMonitoringService] Slow frame detected {"duration_ms":48}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.428497] [PerformanceMonitoringService] Slow frame detected {"duration_ms":51}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.495498] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.562761] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.611339] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.677508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.728532] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.799752] [PerformanceMonitoringService] Slow frame detected {"duration_ms":72}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.861365] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.911455] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:45.977677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:46.028221] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:46.078004] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:46.145294] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:46.210476] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:46.278460] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:46.328011] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:46.377768] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:46.427062] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-19T13:59:49.194969] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.261661] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.310726] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.360883] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.428338] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.494495] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.545169] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.594309] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.661924] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.711299] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.778377] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.828036] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.895199] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.943728] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T13:59:59.994075] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.060882] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.144950] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.195091] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.244547] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.302603] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.328583] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.410912] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.460879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.494141] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.582181] [PerformanceMonitoringService] Slow frame detected {"duration_ms":86}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.660765] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.694267] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.728560] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.761067] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:00.827379] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.077453] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.195312] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.277601] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.395258] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.427879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.530648] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.700061] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.728081] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.794637] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.861028] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:01.927866] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.011943] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.094416] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.152907] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.177720] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.361273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.411136] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.461986] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.810809] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.844870] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.911757] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:02.977993] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:03.011037] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:03.078549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:03.144723] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:03.344957] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:03.394232] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T14:00:04.195300] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: 🐛 DEBUG [2025-07-19T14:00:04.910899] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:04.960808] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:05.912982] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:05.977405] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.027514] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.094750] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.144335] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.178018] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.228428] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.261493] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.295030] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.345099] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.394442] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.444223] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.511107] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.544979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.595199] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.644839] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.707955] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.728375] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.811987] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.861437] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.911945] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.944235] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:06.994858] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:07.028141] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:07.060905] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:07.095201] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:07.128352] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:07.178120] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:07.211192] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:08.861326] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:08.927638] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:08.994714] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.045742] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.111079] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.144511] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.211311] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.244111] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.294527] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.327534] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.360905] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.394624] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.428069] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.495243] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.544435] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.578173] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.611317] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.678191] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.727812] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.761672] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.795487] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:09.845340] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.383511] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.448447] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.511436] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.594298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.645141] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.695482] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.761621] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.811690] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.879149] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.945460] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:14.994585] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.061868] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.112739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.212298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.278671] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.327799] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.378836] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.444534] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.511324] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.577997] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T14:00:15.629126] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-19T14:00:19.195629] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: 🐛 DEBUG [2025-07-19T14:00:25.386325] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-19T14:00:25.410861] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T14:00:25.945814] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T14:00:34.196206] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}