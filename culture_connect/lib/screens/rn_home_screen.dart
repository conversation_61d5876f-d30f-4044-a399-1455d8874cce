import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/rn_components/rn_search_bar.dart';
import 'package:culture_connect/widgets/rn_components/rn_category_pill.dart';
import 'package:culture_connect/widgets/rn_components/rn_quick_action_button.dart';
import 'package:culture_connect/widgets/rn_components/rn_promotional_banner.dart';
import 'package:culture_connect/widgets/rn_components/rn_ai_recommendation_card.dart';
import 'package:culture_connect/widgets/rn_components/rn_destination_card.dart';
import 'package:culture_connect/widgets/rn_components/rn_top_place_card.dart';
import 'package:culture_connect/widgets/rn_components/rn_top_guide_card.dart';

import 'package:culture_connect/screens/luxury_services_screen.dart';

/// Pixel-perfect React Native Home Screen replica
/// Matches the exact design from React Native reference images
class RNHomeScreen extends ConsumerStatefulWidget {
  const RNHomeScreen({super.key});

  @override
  ConsumerState<RNHomeScreen> createState() => _RNHomeScreenState();
}

class _RNHomeScreenState extends ConsumerState<RNHomeScreen> {
  final ScrollController _scrollController = ScrollController();

  // React Native Categories - exact match
  final List<String> _categories = [
    'All',
    'Popular',
    'Cultural',
    'Adventure',
    'Beach',
    'Mountain',
    'City',
    'Food',
  ];

  String _selectedCategory = 'All';
  String _searchQuery = '';
  final int _unreadNotifications = 3; // Mock notification count

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Mock data matching React Native reference
  final List<Map<String, dynamic>> _mockDestinations = [
    {
      'id': '1',
      'title': 'Traditional Tea Ceremony Experience',
      'location': 'Kyoto, Japan',
      'imageUrl':
          'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.8,
      'reviewCount': 124,
      'price': 'From \$45',
      'duration': '2 hours',
      'tags': ['Cultural', 'Traditional', 'Tea'],
    },
    {
      'id': '2',
      'title': 'Street Food Walking Tour',
      'location': 'Bangkok, Thailand',
      'imageUrl':
          'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.9,
      'reviewCount': 89,
      'price': 'From \$35',
      'duration': '3 hours',
      'tags': ['Food', 'Street Food', 'Walking'],
    },
    {
      'id': '3',
      'title': 'Flamenco Dance Workshop',
      'location': 'Seville, Spain',
      'imageUrl':
          'https://images.unsplash.com/photo-1504609813442-a8924e83f76e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.7,
      'reviewCount': 156,
      'price': 'From \$60',
      'duration': '1.5 hours',
      'tags': ['Dance', 'Cultural', 'Workshop'],
    },
  ];

  // Mock data for Top Places section
  final List<Map<String, dynamic>> _mockTopPlaces = [
    {
      'id': '1',
      'title': 'Santorini Sunset Views',
      'location': 'Santorini, Greece',
      'imageUrl':
          'https://images.unsplash.com/photo-1570077188670-e3a8d69ac5ff?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.9,
      'reviewCount': 234,
    },
    {
      'id': '2',
      'title': 'Machu Picchu Ancient Ruins',
      'location': 'Cusco, Peru',
      'imageUrl':
          'https://images.unsplash.com/photo-1587595431973-160d0d94add1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.8,
      'reviewCount': 189,
    },
    {
      'id': '3',
      'title': 'Bali Rice Terraces',
      'location': 'Ubud, Indonesia',
      'imageUrl':
          'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.7,
      'reviewCount': 156,
    },
    {
      'id': '4',
      'title': 'Northern Lights',
      'location': 'Reykjavik, Iceland',
      'imageUrl':
          'https://images.unsplash.com/photo-1531366936337-7c912a4589a7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      'rating': 4.9,
      'reviewCount': 298,
    },
  ];

  // Mock data for Top Guides section - Updated to match reference design
  final List<Map<String, dynamic>> _mockTopGuides = [
    {
      'id': '1',
      'name': 'Nolan W.',
      'specialty': 'Adventure Tours',
      'location': 'Tokyo, Japan',
      'imageUrl':
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
      'rating': 4.8,
      'reviewCount': 93,
      'experience': '12+ years',
      'isCertified': true,
    },
    {
      'id': '2',
      'name': 'Maria Santos',
      'specialty': 'Cultural Heritage',
      'location': 'Barcelona, Spain',
      'imageUrl':
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
      'rating': 4.9,
      'reviewCount': 127,
      'experience': '8+ years',
      'isCertified': true,
    },
    {
      'id': '3',
      'name': 'Elena Rodriguez',
      'specialty': 'Food & Wine',
      'imageUrl':
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
      'rating': 4.9,
      'reviewCount': 156,
      'experience': '10+ years',
    },
    {
      'id': '4',
      'name': 'Ahmed Hassan',
      'specialty': 'Historical Sites',
      'imageUrl':
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=400&q=80',
      'rating': 4.7,
      'reviewCount': 89,
      'experience': '6+ years',
    },
  ];

  void _handleQuickAction(String action) {
    switch (action) {
      case 'hotel':
        // Navigate to hotel booking
        break;
      case 'tours':
        // Navigate to tour packages
        break;
      case 'flights':
        // Navigate to flight booking
        break;
      case 'cars':
        // Navigate to car rental
        break;
      case 'luxury':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const LuxuryServicesScreen()),
        );
        break;
      case 'food':
        // Navigate to food experiences
        break;
      case 'visa':
        // Navigate to visa services
        break;
      case 'medical':
        // Navigate to medical services
        break;
    }
  }

  Future<void> _handleRefresh() async {
    // Simulate refresh
    await Future.delayed(const Duration(seconds: 1));
  }

  void _handleNotificationPress() {
    // Handle notification press - show overlay or navigate
    // TODO: Implement notification overlay
  }

  void _handleDestinationPress(String destinationId) {
    // Navigate to destination details
    // TODO: Navigate to proper destination details screen
    // For now, just show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening destination: $destinationId')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _handleRefresh,
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section - Exact React Native match
                _buildHeader(),

                // Search Bar Section
                _buildSearchSection(),

                // Quick Services Section
                _buildQuickServicesSection(),

                // Promotional Banner Section
                _buildPromotionalBannerSection(),

                // Categories Section
                _buildCategoriesSection(),

                // AI Recommendation Section
                _buildAIRecommendationSection(),

                // Trending Experiences Section
                _buildTrendingExperiencesSection(),

                // Top Places Section
                _buildTopPlacesSection(),

                // Top Guides Section
                _buildTopGuidesSection(),

                // Bottom padding
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top row with greeting and notification
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Hello, Guest',
                style: TextStyle(
                  fontSize: AppTheme.fontSizeLg, // 18px
                  fontWeight: AppTheme.fontWeightMedium,
                  color: AppTheme.textPrimaryColor,
                ),
              ),

              // Notification Button
              GestureDetector(
                onTap: _handleNotificationPress,
                child: Stack(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppTheme.backgroundSecondary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.notifications_outlined,
                        size: 24,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),

                    // Notification Badge
                    if (_unreadNotifications > 0)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: 18,
                          height: 18,
                          decoration: const BoxDecoration(
                            color: AppTheme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              _unreadNotifications > 9
                                  ? '9+'
                                  : _unreadNotifications.toString(),
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Main Title
          const Text(
            'Let\'s Explore the World together',
            style: TextStyle(
              fontSize: AppTheme.fontSizeXxl, // 28px
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: RNSearchBar(
        value: _searchQuery,
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        placeholder: 'Search destination and explore',
        onFilterPressed: () {
          // Handle filter press
          print('Filter pressed');
        },
        onClearPressed: () {
          setState(() {
            _searchQuery = '';
          });
        },
      ),
    );
  }

  Widget _buildQuickServicesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Quick Services',
              style: TextStyle(
                fontSize: AppTheme.fontSizeLg, // 18px
                fontWeight: AppTheme.fontWeightBold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Horizontal scrolling quick actions with defensive constraints
          Container(
            height:
                120, // Increased from 110 to provide extra buffer for all scenarios
            constraints: const BoxConstraints(
              minHeight: 120,
              maxHeight: 120,
            ),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: RNQuickActions.actions.length,
              // Prevent overflow during IndexedStack offstage rendering
              cacheExtent: 0,
              addAutomaticKeepAlives: false,
              addRepaintBoundaries: false,
              itemBuilder: (context, index) {
                final action = RNQuickActions.actions[index];
                return RNQuickActionButton(
                  title: action.title,
                  icon: action.icon,
                  backgroundColor: action.backgroundColor,
                  iconColor: action.iconColor,
                  onPressed: () => _handleQuickAction(action.action),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromotionalBannerSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
      child: RNPromotionalBanner(
        title: 'SAVE UP TO',
        subtitle: '20% WITH Explorer+',
        imageUrl:
            'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
        onPressed: () {
          // Handle promotional banner press
          print('Promotional banner pressed');
        },
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Explore by category',
              style: TextStyle(
                fontSize: AppTheme.fontSizeLg, // 18px
                fontWeight: AppTheme.fontWeightBold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Horizontal scrolling category pills
          SizedBox(
            height: 56,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return RNCategoryPill(
                  label: category,
                  isSelected: _selectedCategory == category,
                  onPressed: () {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIRecommendationSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
      child: RNAIRecommendationCard(
        title: 'Your Perfect Weekend Getaway',
        description:
            'Based on your preferences, we think you will love this cultural experience in Kyoto.',
        imageUrl:
            'https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
        onPressed: () {
          // Handle AI recommendation press
          _handleDestinationPress('1');
        },
      ),
    );
  }

  Widget _buildTrendingExperiencesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Trending Experiences',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLg, // 18px
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // Handle see all press
                    print('See all pressed');
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      fontWeight: AppTheme.fontWeightMedium,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Destination Cards
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: _mockDestinations.map((destination) {
                return RNDestinationCard(
                  title: destination['title'],
                  location: destination['location'],
                  imageUrl: destination['imageUrl'],
                  rating: destination['rating'],
                  reviewCount: destination['reviewCount'],
                  price: destination['price'],
                  duration: destination['duration'],
                  tags: List<String>.from(destination['tags']),
                  onPressed: () => _handleDestinationPress(destination['id']),
                  onFavoritePressed: () {
                    // Handle favorite press
                    print('Favorite pressed for ${destination['id']}');
                  },
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopPlacesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Top Places',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLg, // 18px
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // Handle see all press
                    print('Top Places - See all pressed');
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      fontWeight: AppTheme.fontWeightMedium,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Horizontal Scrolling Places Cards
          SizedBox(
            height: 180, // Fixed height for horizontal scrolling
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _mockTopPlaces.length,
              itemBuilder: (context, index) {
                final place = _mockTopPlaces[index];
                return RNTopPlaceCard(
                  title: place['title'],
                  location: place['location'],
                  imageUrl: place['imageUrl'],
                  rating: place['rating'],
                  reviewCount: place['reviewCount'],
                  onPressed: () => _handleDestinationPress(place['id']),
                  onFavoritePressed: () {
                    // Handle favorite press
                    print('Favorite pressed for place ${place['id']}');
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopGuidesSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Top Guides',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLg, // 18px
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // Handle see all press
                    print('Top Guides - See all pressed');
                  },
                  child: const Text(
                    'See All',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSm, // 14px
                      fontWeight: AppTheme.fontWeightMedium,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Horizontal Scrolling Guide Cards
          SizedBox(
            height:
                252, // Optimized height to accommodate all card elements (was 240px)
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _mockTopGuides.length,
              itemBuilder: (context, index) {
                final guide = _mockTopGuides[index];
                return RNTopGuideCard(
                  name: guide['name'],
                  specialty: guide['specialty'],
                  location:
                      guide['location'], // Added required location parameter
                  imageUrl: guide['imageUrl'],
                  rating: guide['rating'],
                  reviewCount: guide['reviewCount'],
                  experience: guide['experience'],
                  isCertified: guide['isCertified'] ??
                      true, // Added certification status
                  onPressed: () => _handleGuidePress(guide['id']),
                  onFavoritePressed: () {
                    // Handle favorite press
                    print('Favorite pressed for guide ${guide['id']}');
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _handleGuidePress(String guideId) {
    // Navigate to guide details
    // TODO: Navigate to proper guide details screen
    // For now, just show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening guide: $guideId')),
    );
  }
}
